```
type ArticleDTO = null | {
  title: string;
  category: { id: string; name: string; urlAlias: string };
  body: string;
  // ..rest of the properties
};

const ArticleCtx = React.createContext<ArticleDTO>(null);

const Provider: React.FC<{
  data: NewsArticle | Commentary | Sponsored;
  children: React.ReactNode;
}> = (props) => {
  const data = mapToDTO(props.data);

  return (
    <ArticleCtx.Provider value={{ data }}>{props.children}</ArticleCtx.Provider>
  );
};

const Title: FC<{ className?: string }> = ({ className }) => {
  const { title } = useContext(ArticleCtx);

  return <h1 className={classNames(baseStylesGoesHere, className)}>{title}</h1>;
};

const Category: FC<{ className?: string }> = ({ className }) => {
  const { category } = useContext(ArticleCtx);

  return (
    <Link href={category.urlAlias}>
      <span className={classNames(baseStylesGoHere, className)}>
        {category.name}
      </span>
    </Link>
  );
};

export { Provider, Title, Category };

// USAGE
import * as ArticleCtx from "components/ArticleCtx";

const NewsArticlePage: NextPage = () => {
  const data = kitcoQuery(news.articleById(id));

  return (
    <ArticleCtx.Provider data={data}>
      <ArticleCtx.Title />
      <ArticleCtx.Category />
    </ArticleCtx.Provider>
  );
};

export default NewsArticlePage;
```
