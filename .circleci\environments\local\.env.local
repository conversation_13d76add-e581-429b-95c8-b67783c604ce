NEXT_PUBLIC_URL=https://localhost:3000

##########################################
#   SITE CONFIGURATION
##########################################

# Default timezone
NEXT_PUBLIC_TIMEZONE="America/New_York"

# Markets Open/Closed hours
NEXT_PUBLIC_MARKETS_OPEN_HOUR=18
NEXT_PUBLIC_MARKETS_CLOSE_HOUR=17
# Sunday is 0
NEXT_PUBLIC_MARKETS_OPEN_DAY=0
# Friday
NEXT_PUBLIC_MARKETS_CLOSE_DAY=5

# Show login menu
NEXT_PUBLIC_LOGIN_MENU="true"

# Enable/disable Coraltalk
NEXT_PUBLIC_CORALTALK_ENABLED="true"

# Enable dev JS maps for dev environment
NEXT_PUBLIC_ENABLE_DEV_MAPS="true"

##########################################
#   INTERNAL SERVICES
##########################################
NEXT_PUBLIC_DRUPAL_URI=https://cms-drupal.dev.kitco.com
NEXT_PUBLIC_GRAPH_GATEWAY_URL=https://kdb-gw.dev.kitco.com
NEXT_PUBLIC_GRAPH_NEWS_URL=https://cms-drupal.dev.kitco.com/graphql
NEXT_PUBLIC_GW_GQL_URI=https://kdb-gw.dev.kitco.com/graphql
NEXT_PUBLIC_IMAGES_CDN_API=https://image-resizer.dev.kitco.com

# Coraltalk
NEXT_PUBLIC_CORALTALK_URL=https://talk.dev.kitco.com
CORALTALK_JWT_SECRET="ssosec_e3cfc1ffa349467f363a2383e4844bef8448bbb608c1c75131b38baccbec78eb39236a46"

# Discourse
DISCOURSE_API_KEY="be91a99c392a2ff9575d2ca1205dbd20bfa78ab92851a3916e6f4e86771ac26a"
DISCOURSE_API_USERNAME="dev"
DISCOURSE_BASIC_AUTH_PASS="replatform!"
DISCOURSE_BASIC_AUTH_USER="favish"
DISCOURSE_SSO_SECRET="jkb1urm0uqj*BHZ@bcz"
NEXT_PUBLIC_DISCOURSE_URL="http://forum.kitco.local.favish.com/"

##########################################
#   EXTERNAL SERVICES
##########################################

# Barchart
NEXT_PUBLIC_BARCHART_API_KEY=37800fbe8be1378806120895e41d12fe

# Cloudflare
NEXT_PUBLIC_URL_CLOUDFLARE=https://mail.edge-kitco.com

# Contact/Feedback Form encryption
NEXT_PUBLIC_KEY_ENCRYPT=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCX4YxK2HnmQmHzocYXSMkixATAKBLUgx5P/qQUH/1dmUU+aPvL7QiPNBSjP5aUD9MjVq++w70f3X3RUQP5NS5VvkxTsad7Kg+I3yZ6gCRmTG0tGbVkDfSgO1fstN4jTOI6yM8fY+/RfJbyRgTu7osXRAG5xANFysTrHudDrmBJ5QIDAQAB

# Google Firebase
NEXT_PUBLIC_FIREBASE_API_KEY="AIzaSyAlE3D_Zyeae4EPolo5Ba8UdFxIcEPOUxU"
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="kitco-dev-5346b.firebaseapp.com"
NEXT_PUBLIC_FIREBASE_DATABASE_URL="https://kitco-dev-5346b-default-rtdb.firebaseio.com"
NEXT_PUBLIC_FIREBASE_PROJECT_ID="kitco-dev-5346b"
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET="kitco-dev-5346b.appspot.com"
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID="580976841852"
NEXT_PUBLIC_FIREBASE_APP_ID="1:580976841852:web:e6268345780fa73fdb65f9"
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID="G-SK768PRXG5"

# Google Recaptcha (ToDo: We should use only one key)
NEXT_PUBLIC_CAPTCHA_SITE_KEY=6Ldn0iMUAAAAAOEe5qlxlcXwSoTwRXs1aeu4VJcT
NEXT_PUBLIC_CAPTCHA_SECRET_KEY=6Ldn0iMUAAAAAAaZ8Z_vaAUIiDDsXAb8ZHROSdas
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=6Lec7KwpAAAAAOnCYW9VgHSlFblbuzzZ7vCNYsQw
RECAPTCHA_SECRET_KEY=6Lec7KwpAAAAAAhl4JPsRP3xJtkZ4hFiVCcUmTgg

# Google Storage
NEXT_PUBLIC_BUCKET=kitco-cms-local
NEXT_PUBLIC_VIDEO_BUCKET=kitco-video-dev
NEXT_PUBLIC_AUDIO_BUCKET=kitco-audio-dev
NEXT_PUBLIC_VCMS_BUCKET=https://storage.googleapis.com/kitco-video-dev
NEXT_PUBLIC_ACMS_BUCKET=https://storage.googleapis.com/kitco-audio-dev

# Mailgun
MAILGUN_API_KEY="**************************************************"
MAILGUN_DOMAIN="mg.kitco.com"