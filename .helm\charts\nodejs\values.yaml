global:
  hostname: "localhost"

image:
  repository: "node"
  tag: "20"

# Only used when autoscaling is disabled.
replicas: 1

autoscaling:
  # Enables or disables autoscaling for the deployment
  enabled: false

  # The minimum number of pod replicas that the HPA will maintain
  minReplicas: 1

  # The maximum number of pod replicas that the HPA can scale up to
  maxReplicas: 10

  targets:
    # The target average CPU utilization percentage for scaling decisions
    cpuUtilizationPercentage: 60

    # The target average memory utilization percentage for scaling decisions
    memoryUtilizationPercentage: 60

  # The stabilization window (in seconds) before scaling up, to prevent rapid fluctuations
  scaleUpStabilizationWindowSeconds: 0

  # The maximum percentage by which the number of pods can increase during each scale-up operation
  scaleUpPercent: 100

  # The period (in seconds) over which the scale-up calculation is applied
  scaleUpPeriodSeconds: 15

  # The stabilization window (in seconds) before scaling down, to prevent rapid fluctuations
  scaleDownStabilizationWindowSeconds: 300

  # The maximum percentage by which the number of pods can decrease during each scale-down operation
  scaleDownPercent: 50

  # The period (in seconds) over which the scale-down calculation is applied
  scaleDownPeriodSeconds: 60

nodeEnv: "development"
nodeSelector: {}
port: "3000"
host: "0.0.0.0"
containerName: "nodejs"
extraEnvs: []
# extraEnvs:
#   - name: TEST
#     value: "thisisatest"

livenessProbe:
  enabled: true
  path: "/"
  port: 3000
  initialDelaySeconds: 30
  periodSeconds: 30
  failureThreshold: 6

resources: {}
tolerations: []
volume:
  enabled: false
  hostPath: ""
  command: []
  commandArgs: []
extraVolumes: 
  #- mountPath: /usr/src/app/db-models
  #name: db-models
extraVolumeMounts: 
  # - mountPath: /usr/src/app/db-models
  #   name: db-models

basicAuth:
  enabled:
  username:
  password:
  
ingress:
  hostname: "localhost"
  # If true, redirect requests to www.  Useful for dev/staging when www is not utilized.
  # Consumed both by ingress and varnish
  redirect_www: false
  cluster_container_cidr: "***********/16"
  enabled: true
  # Ingress class - usually either gce or nginx - default nginx
  class: "nginx"
  tls:
    enabled: true
    # Cert-manager can be optionally used INSTEAD of the custom crt/key combo. Defaults to true.
    cert_manager:
      enabled: true
      # We assume you're using a cluster issuer
      cluster_issuer: "letsencrypt-prod"
    # Adding a custom cert will only be used if cert-manager is disabled.
    custom_cert:
      crt:
      key:
    # Custom annotations for different environments like GKE and EKS.
    annotations: { }
