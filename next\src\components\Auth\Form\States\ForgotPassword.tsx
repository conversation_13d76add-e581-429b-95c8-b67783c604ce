import * as process from 'process'
import type React from 'react'
import { useEffect, useState } from 'react'
import { BsEnvelope } from 'react-icons/bs'
import Input from '~/src/components/Auth/Form/Elements/Input'
import SubmitButton from '~/src/components/Auth/Form/Elements/SubmitButton'
import type { UserData } from '~/src/components/Auth/Types/UserData'
import { useRecaptchaVerification } from '~/src/features/auth/recapcha'
import Validator from '~/src/features/auth/validator'
import { resetPassword } from '~/src/services/firebase/service'

/**
 * Props for the ForgotPasswordForm component.
 */
interface ForgotPasswordFormProps {
  onResetLinkSent?: () => void
  onBackToLogin?: () => void
  onError?: (error: string) => void
  userData?: UserData
  redirectToForum?: boolean
}

/**
 * Forgot password form component.
 *
 * @param {ForgotPasswordFormProps} props - Component properties
 * @returns {React.ReactElement} - Rendered component
 */
const ForgotPassword: React.FC<ForgotPasswordFormProps> = ({
  onResetLinkSent,
  onBackToLogin,
  onError,
  userData,
  redirectToForum = false,
}: ForgotPasswordFormProps): React.ReactElement => {
  // State for the form submission.
  const [isSending, setIsSending] = useState<boolean>(false)

  // State for the email field.
  const [email, setEmail] = useState<string>('')

  // State for any error messages.
  const [emailError, setEmailError] = useState<string>()

  // State for showing the reset link sent message.
  const [showResetLinkSent, setShowResetLinkSent] = useState<boolean>(false)

  // Custom hook to verify the user with reCAPTCHA
  const verifyUserRecaptcha = useRecaptchaVerification()

  /**
   * Handle the email field change.
   */
  useEffect(() => {
    if (userData?.email && typeof userData?.email === 'string') {
      setEmail(userData?.email || '')
    } else {
      setEmail('')
    }
  }, [userData])

  /**
   * Handle the email field change.
   *
   * @param e
   */
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail: string = e.target.value.toString().toLowerCase().trim()
    setEmail(newEmail)
    if (!Validator.validateEmail(newEmail)) {
      setEmailError('Please enter a valid email.')
    } else {
      setEmailError(null)
    }
  }

  /**
   * Handle the form submission.
   *
   * @param e
   */
  const handleSubmit = async (e): Promise<void> => {
    // Prevent the form from refreshing the page.
    e.preventDefault()

    // Validate the email field.
    setIsSending(true)

    // Clear any previous error messages.
    onError('')

    try {
      // Verify the user with reCAPTCHA
      const recaptchaVerified = await verifyUserRecaptcha()

      // If the reCAPTCHA verification fails, show an error message and return.
      if (!recaptchaVerified) {
        onError('The reCAPTCHA verification failed. Please try again.')
        return
      }

      // Send the password reset email.
      await resetPassword(
        email,
        redirectToForum
          ? process.env.NEXT_PUBLIC_DISCOURSE_URL
          : process.env.NEXT_PUBLIC_URL,
      )

      // Show the success message.
      setShowResetLinkSent(true)
    } catch (error) {
      console.error('Error sending password reset email:', error)
      onError('Failed to send password reset email. Please try again.')
    } finally {
      setIsSending(false)
    }
  }

  return (
    <div className="sm:mx-auto sm:w-full sm:max-w-sm">
      {!showResetLinkSent ? (
        <form onSubmit={handleSubmit} className="mt-4 space-y-6">
          <Input
            label="Email address"
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required={true}
            value={email}
            error={emailError}
            onChange={handleEmailChange}
          />

          <SubmitButton
            isSending={isSending}
            disabledCondition={!!emailError || isSending}
            buttonText="Send Password Reset Link"
            loadingText="Sending..."
          />

          <div className="mt-4 text-center text-sm">
            <button
              type="button"
              onClick={onBackToLogin}
              className="font-semibold text-ktc-blue hover:text-ktc-black"
            >
              Back to Login
            </button>
          </div>
        </form>
      ) : (
        <div className="text-center">
          <div className="relative flex h-24 w-full items-center justify-center">
            <div className="absolute flex items-center justify-center">
              <div className="flex h-24 w-24 items-center justify-center bg-white">
                <BsEnvelope className="text-6xl text-ktc-blue" />
              </div>
            </div>
          </div>
          <h3 className="mb-4 text-lg font-bold">
            You have requested to reset your password
          </h3>
          <p>Please check your email to reset your password.</p>
          <button
            type="button"
            onClick={onResetLinkSent}
            className="mt-4 font-semibold text-ktc-blue hover:text-ktc-black"
          >
            Back to Login
          </button>
        </div>
      )}
    </div>
  )
}

export default ForgotPassword
