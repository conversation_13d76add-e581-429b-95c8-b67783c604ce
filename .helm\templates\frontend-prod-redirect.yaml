# Redirect traffic from frontend.prod.kitco.com to www.kitco.com

{{- if .Values.frontendProdRedirect.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-prod-redirect
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    certmanager.k8s.io/cluster-issuer: letsencrypt-prod
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/permanent-redirect: https://www.kitco.com$request_uri
    nginx.ingress.kubernetes.io/permanent-redirect-code: '308'
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  tls:
    - hosts:
        - "frontend.prod.kitco.com"
      secretName: "frontend-prod-redirect-tls"
  rules:
    - host: "frontend.prod.kitco.com"
      http:
        paths:
          - backend:
              service:
                name: "kitco-frontend-prod-nextjs"
                port:
                  number: 80
            pathType: ImplementationSpecific
{{- end }}