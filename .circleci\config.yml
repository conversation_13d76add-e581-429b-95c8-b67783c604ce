version: 2.1

parameters:
  cluster:
    type: string
    default: "kitco"
  project-id:
    type: string
    default: "kitco-224816"
  project:
    type: string
    default: "kitco-frontend"
  helm-version:
    type: string
    default: v3.15.3

orbs:
  helm: circleci/helm@3.0.2
  gcp-gcr: circleci/gcp-gcr@0.16.3
  gcp-gke: circleci/gcp-gke@2.2.0
  gcp-cli: circleci/gcp-cli@3.2.1

jobs:
  next-build-and-push:
    executor: gcp-gcr/default
    steps:
      - checkout
      - run:
          name: "Generate correct .env file based on branch"
          command: |
            if [ "${CIRCLE_BRANCH}" = "prod" ]; then
              echo "CircleCI: Copying prod .env file"
              cp ./.circleci/environments/prod/.env.production ./next/.env.production
            else
              echo "CircleCI: Copying dev .env file"
              cp ./.circleci/environments/dev/.env.development ./next/.env
            fi
      - run:
          name: "Copy Firebase admin auth file based on branch"
          command: |
            if [ "${CIRCLE_BRANCH}" = "prod" ]; then
              echo "CircleCI: Copying prod Firebase Auth file"
              cp ./.circleci/environments/prod/firebase-admin.json ./next/secrets/firebase-admin.json
            else
              echo "CircleCI: Copying dev Firebase Auth file"
              cp ./.circleci/environments/dev/firebase-admin.json ./next/secrets/firebase-admin.json
            fi
      - gcp-gcr/build-image:
          image: << pipeline.parameters.project >>
          path: "next"
          docker-context: "next"
          tag: ${CIRCLE_BRANCH}-${CIRCLE_SHA1}
          registry-url: "us.gcr.io"
      - gcp-gcr/gcr-auth
      - gcp-gcr/push-image:
          image: << pipeline.parameters.project >>
          tag: ${CIRCLE_BRANCH}-${CIRCLE_SHA1}
          registry-url: "us.gcr.io"

  upgrade-helm-chart:
    executor: gcp-gke/default
    steps:
      - checkout
      - gcp-cli/setup:
          components: "gke-gcloud-auth-plugin kubectl"
      - gcp-gke/update-kubeconfig-with-credentials:
          cluster: << pipeline.parameters.cluster >>
      - helm/upgrade_helm_chart:
          chart: .helm
          helm_version: << pipeline.parameters.helm-version >>
          namespace: kitco-frontend-${CIRCLE_BRANCH}
          release_name: << pipeline.parameters.project >>-${CIRCLE_BRANCH}
          values: .circleci/environments/${CIRCLE_BRANCH}/values.yml
          values_to_override: "nextjs.image.tag=${CIRCLE_BRANCH}-${CIRCLE_SHA1}"
          wait: true
          debug: true

  purge-varnish-cache:
    executor: gcp-gke/default
    steps:
      - checkout
      - gcp-cli/setup:
          components: "gke-gcloud-auth-plugin kubectl"
      - gcp-gke/update-kubeconfig-with-credentials:
          cluster: << pipeline.parameters.cluster >>
      - run:
          name: Set Varnish Servers Environment Variable
          command: |
            NAMESPACE="kitco-frontend-${CIRCLE_BRANCH}"
            VARNISH_POD_NAMES=$(kubectl get pods -n $NAMESPACE -l service=varnish -o jsonpath='{.items[*].metadata.name}')
            # transform pods name to pods endpoint
            VARNISH_SERVERS=$(echo "$VARNISH_POD_NAMES" | sed "s/\([^ ]*\)/\1.varnish-internal.$NAMESPACE.svc.cluster.local/g")
            echo "export VARNISH_SERVERS=\"$VARNISH_SERVERS\"" >> $BASH_ENV
            source $BASH_ENV
      - run:
          name: Delete existing purge-varnish-cache Job if it exists
          command: |
            kubectl -n kitco-frontend-${CIRCLE_BRANCH} delete job purge-varnish-cache --ignore-not-found=true
      - run:
          name: Apply Kubernetes Job to Purge Varnish Cache
          command: |
            sed "s|%%VARNISH_SERVERS%%|$VARNISH_SERVERS|" .helm/deploy-jobs/purge-varnish-cache-job.yaml > purge-varnish-cache-rendered.yaml
            kubectl -n kitco-frontend-${CIRCLE_BRANCH} apply -f purge-varnish-cache-rendered.yaml
            kubectl -n kitco-frontend-${CIRCLE_BRANCH} wait --for=condition=complete job/purge-varnish-cache
            kubectl -n kitco-frontend-${CIRCLE_BRANCH} logs job/purge-varnish-cache
            
            if [ $? -ne 0 ]; then
              echo "Job failed to complete in time"
              exit 1
            fi
            
            kubectl -n kitco-frontend-${CIRCLE_BRANCH} delete job purge-varnish-cache

workflows:
  frontend-workflow:
    jobs:
      - next-build-and-push:
          filters:
            branches:
              only:
                - dev
                - prod
      - upgrade-helm-chart:
          requires:
            - next-build-and-push
      - purge-varnish-cache:
          requires:
            - upgrade-helm-chart
