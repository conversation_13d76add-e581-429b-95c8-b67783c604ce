import { onAuthStateChanged } from 'firebase/auth'
import { useEffect, useState } from 'react'
import Avatar from '~/src/components/Auth/EditProfile/Avatar'
import Input from '~/src/components/Auth/Form/Elements/Input'
import { auth } from '~/src/services/firebase/config'
import { updateProfile } from '~/src/services/firebase/service'

const EditProfile = () => {
  const [formData, setFormData] = useState({
    email: '',
    displayName: '',
    username: '',
    password: '',
  })
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    // Wait for firebase auth to be initialized
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        setLoading(true)
        setFormData({
          email: auth.currentUser.email || '',
          displayName: auth.currentUser.displayName || '',
          username: '', //  getUsername(auth.currentUser.uid) ||
          password: '',
        })
        setLoading(false)
      }
    })

    // Clean up the subscription
    return () => unsubscribe()
  }, [])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    try {
      await updateProfile(auth.currentUser, formData)
      alert('Profile updated successfully!')
    } catch (error) {
      console.log('Failed to update profile. ' + error.message)
    }
    setLoading(false)
  }

  if (loading) return <p>Loading...</p>

  return (
    <>
      <div className="grid w-full grid-cols-1 border-0 border-solid px-4 pt-6 leading-6 text-black xl:grid-cols-3 xl:gap-4">
        <div className="col-span-full mb-4 border-0 border-solid text-black xl:mb-2">
          <nav
            className="mb-5 flex border-0 border-solid"
            aria-label="Breadcrumb"
          >
            <ol className="m-0 inline-flex items-center border-0 border-solid p-0">
              <li className="inline-flex items-center border-0 border-solid text-left">
                <a
                  href="#"
                  className="inline-flex cursor-pointer items-center border-0 border-solid text-gray-700"
                >
                  <svg
                    className="mr-2 block h-5 w-5 border-0 border-solid align-middle"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
                      className="border-0 border-solid"
                    />
                  </svg>
                  Home
                </a>
              </li>
              <li className="ml-2 mr-0 border-0 border-solid text-left md:ml-2 md:mr-0">
                <div className="flex items-center border-0 border-solid">
                  <svg
                    className="block h-6 w-6 border-0 border-solid align-middle text-gray-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                      className="border-0 border-solid"
                    />
                  </svg>
                  <a
                    href="#"
                    className="ml-1 cursor-pointer border-0 border-solid text-sm font-medium leading-5 text-gray-700 md:ml-2"
                  >
                    Users
                  </a>
                </div>
              </li>
              <li className="ml-2 mr-0 border-0 border-solid text-left md:ml-2 md:mr-0">
                <div className="flex items-center border-0 border-solid">
                  <svg
                    className="block h-6 w-6 border-0 border-solid align-middle text-gray-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                      className="border-0 border-solid"
                    />
                  </svg>
                  <span
                    className="ml-1 border-0 border-solid text-sm font-medium leading-5 text-gray-400 md:ml-2"
                    aria-current="page"
                  >
                    Settings
                  </span>
                </div>
              </li>
            </ol>
          </nav>
          <h1 className="m-0 border-0 border-solid text-xl font-semibold leading-7 text-gray-900 sm:text-2xl sm:leading-8">
            User settings
          </h1>
        </div>
        {/* Right Content */}
        <div className="col-span-full border-0 border-solid text-black shadow xl:col-auto">
          <div className="mb-4 rounded-lg border-0 border-solid bg-white p-4 sm:p-6 xl:p-8">
            <Avatar email={formData.email} displayName={formData.displayName} />
          </div>
          {/*
          <div className="mb-4 rounded-lg border-0 border-solid bg-white p-4 sm:p-6 xl:p-8">
            <RegionalSettings />
          </div>
          <div className="mb-4 rounded-lg border-0 border-solid bg-white p-4 sm:p-6 xl:p-8">
            <SocialConnect />
          </div>
          <div className="mb-4 rounded-lg border-0 border-solid bg-white p-4 sm:p-6 xl:p-8">
            <OtherAccounts />
          </div>
          */}
        </div>
        <div className="col-span-2 border-0 border-solid text-black">
          <div className="mb-4 rounded-lg border-0 border-solid bg-white p-4 sm:p-6 xl:p-8">
            <h3 className="mx-0 mb-4 mt-0 border-0 border-solid text-xl font-bold leading-7">
              General information
            </h3>
            <form action={handleSubmit} className="border-0 border-solid">
              <div className="grid grid-cols-6 gap-6 border-0 border-solid">
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <Input
                    label="Display Name"
                    id="display_name"
                    name="display_name"
                    type="text"
                    autoComplete="name"
                    required={true}
                    value={formData.displayName}
                    onChange={handleChange}
                    error={null}
                  />
                </div>
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <Input
                    label="Username"
                    id="username"
                    name="username"
                    type="text"
                    autoComplete="username"
                    required={true}
                    value={formData.username}
                    disabled={false}
                    onChange={handleChange}
                    error={null}
                  />
                </div>
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <Input
                    label="Email address"
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required={true}
                    value={formData.email}
                    onChange={handleChange}
                    error={null}
                    disabled={true}
                    hint={'Email address cannot be changed at this time'}
                  />
                </div>
                {/*
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <label
                    htmlFor="city"
                    className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
                  >
                    City
                  </label>
                  <input
                    type="text"
                    name="city"
                    id="city"
                    className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
                    placeholder="e.g. San Francisco"
                    required=""
                  />
                </div>
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <label
                    htmlFor="address"
                    className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
                  >
                    Address
                  </label>
                  <input
                    type="text"
                    name="address"
                    id="address"
                    className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
                    placeholder="e.g. California"
                    required=""
                  />
                </div>
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <label
                    htmlFor="country"
                    className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
                  >
                    Country
                  </label>
                  <input
                    type="text"
                    name="country"
                    id="country"
                    className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
                    placeholder="United States"
                    required=""
                  />
                </div>
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <label
                    htmlFor="phone-number"
                    className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
                  >
                    Phone Number
                  </label>
                  <input
                    type="number"
                    name="phone-number"
                    id="phone-number"
                    className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
                    placeholder="e.g. +(12)3456 789"
                    required=""
                  />
                </div>
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <label
                    htmlFor="birthday"
                    className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
                  >
                    Birthday
                  </label>
                  <input
                    type="number"
                    name="birthday"
                    id="birthday"
                    className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
                    placeholder="15/08/1990"
                    required=""
                  />
                </div>
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <label
                    htmlFor="organization"
                    className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
                  >
                    Organization
                  </label>
                  <input
                    type="text"
                    name="organization"
                    id="organization"
                    className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
                    placeholder="Company Name"
                    required=""
                  />
                </div>
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <label
                    htmlFor="role"
                    className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
                  >
                    Role
                  </label>
                  <input
                    type="text"
                    name="role"
                    id="role"
                    className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
                    placeholder="React Developer"
                    required=""
                  />
                </div>
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <label
                    htmlFor="department"
                    className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
                  >
                    Department
                  </label>
                  <input
                    type="text"
                    name="department"
                    id="department"
                    className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
                    placeholder="Development"
                    required=""
                  />
                </div>
                <div className="col-span-6 border-0 border-solid sm:col-span-3">
                  <label
                    htmlFor="zip-code"
                    className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
                  >
                    Zip/postal code
                  </label>
                  <input
                    type="number"
                    name="zip-code"
                    id="zip-code"
                    className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
                    placeholder={123456}
                    required=""
                  />
                </div>
                */}
                <div className="col-span-6 border-0 border-solid">
                  <button
                    className="m-0 cursor-pointer rounded-lg border-0 border-solid bg-blue-700 bg-none px-5 py-2 text-center text-sm font-medium normal-case leading-5 text-white"
                    type="submit"
                  >
                    Save all
                  </button>
                </div>
              </div>
            </form>
          </div>
          {/*
          <div className="mb-4 rounded-lg border-0 border-solid bg-white p-4 sm:p-6 xl:p-8">
            <EditPassword />
          </div>
          <div className="mb-4 rounded-lg border-0 border-solid bg-white p-4 sm:p-6 xl:mb-0 xl:p-8">
            <Sessions />
          </div>
          */}
        </div>
      </div>
    </>
  )
}

export default EditProfile
