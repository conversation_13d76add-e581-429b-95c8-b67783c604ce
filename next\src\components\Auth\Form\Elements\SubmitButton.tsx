import clsx from 'clsx'
import type React from 'react'
import { FaSpinner } from 'react-icons/fa'

/**
 * SubmitButton component
 *
 * @param {boolean} isSending - Loading state of the button
 * @param {string} buttonText - Text to display on the button
 * @param {string} loadingText - Text to display when loading
 * @param {boolean} disabledCondition - Condition to disable the button
 * @param {string} className - Custom class name for the button
 * @param {() => void} onClick - Function to run on button click
 * @param {'button' | 'submit' | 'reset'} type - Button type
 */
interface SubmitButtonProps {
  isSending?: boolean
  className?: string
  buttonText: string
  loadingText?: string
  disabledCondition?: boolean
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
}

/**
 * SubmitButton component
 *
 * @param {SubmitButtonProps} props - Component properties
 * @returns {React.ReactElement} - Rendered component
 */
const SubmitButton: React.FC<SubmitButtonProps> = ({
  isSending = false,
  className,
  buttonText,
  loadingText = '',
  disabledCondition = false,
  onClick,
  type = 'submit',
}: SubmitButtonProps): React.ReactElement => (
  <button
    type={type}
    onClick={onClick}
    className={
      className
        ? className
        : clsx(
            'flex w-full items-center justify-center gap-4 rounded-md ' +
              'px-3 py-1.5 text-sm font-semibold leading-6 shadow-sm ' +
              'focus-visible:outline focus-visible:outline-2 ' +
              'focus-visible:outline-offset-2 focus-visible:outline-ktc-blue',
            {
              'bg-ktc-blue text-white hover:bg-ktc-black hover:text-white':
                !disabledCondition,
              'cursor-not-allowed bg-zinc-300 text-stone-500':
                disabledCondition,
            },
          )
    }
    disabled={disabledCondition}
  >
    {isSending && <FaSpinner className="animate-spin" />}
    {isSending ? loadingText : buttonText}
  </button>
)

export default SubmitButton
