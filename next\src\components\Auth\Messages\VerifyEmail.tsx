import type { User } from 'firebase/auth'
import React from 'react'
import { BsEnvelope } from 'react-icons/bs'
import SubmitButton from '~/src/components/Auth/Form/Elements/SubmitButton'
import { AlertType } from '~/src/components/Auth/Messages/Alert'
import { sendEmailVerification } from '~/src/services/firebase/service'

/**
 * VerifyEmailProps
 *
 * user: User (from firebase)
 * onBack: Is a function that will be called when the user clicks the back button
 * onAlert?: Is a function that will be called when an alert is needed
 */
interface VerifyEmailProps {
  onAlert?: (message: string, type?: AlertType) => void
  onBack: () => void
  redirectToForum?: boolean
  user: User
}

/**
 * VerifyEmail Component
 *
 * @param {VerifyEmailProps} props
 * @returns {React.ReactElement} React Component
 */
const VerifyEmail: React.FC<VerifyEmailProps> = ({
  onAlert,
  onBack,
  redirectToForum = false,
  user,
}: VerifyEmailProps): React.ReactElement => {
  const [isSending, setIsSending] = React.useState(false)

  /**
   * handleSendEmailVerification is a function that will be called when the user
   * clicks the resend verification email button
   *
   * It will send a verification email to the user
   *
   * @returns {Promise<void>}
   */
  const handleSendEmailVerification = async (): Promise<void> => {
    // Set isSending to true
    setIsSending(true)

    // Send the verification email to the user
    sendEmailVerification(
      user,
      redirectToForum
        ? process.env.NEXT_PUBLIC_DISCOURSE_URL
        : process.env.NEXT_PUBLIC_URL,
    )
      .then(() => {
        onAlert(
          'Verification Email Sent! Please check your email',
          AlertType.SUCCESS,
        )
      })
      .catch((error) => {
        onAlert('Error sending verification email')
        console.error('Error sending verification email', error)
      })
      .finally(() => setIsSending(false))
  }

  return (
    <div className="sm:mx-auto sm:w-full sm:max-w-sm text-center">
      <div className="relative flex h-36 w-full items-center justify-center">
        <div className="absolute flex items-center justify-center">
          <div className="flex h-24 w-24 items-center justify-center rounded-full border-2 border-gray-300 bg-white">
            <BsEnvelope className="text-6xl text-ktc-blue" />
          </div>
        </div>
      </div>

      <h3 className="mb-4 text-lg font-bold">You&apos;re almost there.</h3>
      <p>
        In order to start using your Kitco account, you need to confirm your
        email address. Please check your email for a verification link.
      </p>
      <p className="mt-4 text-xs text-gray-500">
        You did not receive the email? Click the button below to resend the
        email.
      </p>
      <div className="mt-4 flex flex-col">
        <SubmitButton
          isSending={isSending}
          buttonText="Resend verification email"
          loadingText="Sending email"
          disabledCondition={isSending}
          onClick={handleSendEmailVerification}
          type="button"
        />
        <button
          type="button"
          onClick={onBack}
          className="mt-4 font-semibold text-ktc-blue hover:text-ktc-black"
        >
          Back to Login
        </button>
      </div>
    </div>
  )
}

export default VerifyEmail
