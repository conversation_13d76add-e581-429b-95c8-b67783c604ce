.clickable {
  cursor: pointer;
}

.contentContainer {
  position: relative;
  height: 4rem;
  overflow: hidden;
}

/* Base slide styles */
.contentSlide {
  position: absolute;
  width: 100%;
  transition: transform 0.5s ease-out;
  will-change: transform;
}

/* Generate transform classes for up to 20 slides */
/* All slides should be positioned at translateY(0) to be visible in the container */
.slide0 {
  transform: translateY(0);
}
.slide1 {
  transform: translateY(0);
}
.slide2 {
  transform: translateY(0);
}
.slide3 {
  transform: translateY(0);
}
.slide4 {
  transform: translateY(0);
}
.slide5 {
  transform: translateY(0);
}
.slide6 {
  transform: translateY(0);
}
.slide7 {
  transform: translateY(0);
}
.slide8 {
  transform: translateY(0);
}
.slide9 {
  transform: translateY(0);
}
.slide10 {
  transform: translateY(0);
}
.slide11 {
  transform: translateY(0);
}
.slide12 {
  transform: translateY(0);
}
.slide13 {
  transform: translateY(0);
}
.slide14 {
  transform: translateY(0);
}
.slide15 {
  transform: translateY(0);
}
.slide16 {
  transform: translateY(0);
}
.slide17 {
  transform: translateY(0);
}
.slide18 {
  transform: translateY(0);
}
.slide19 {
  transform: translateY(0);
}

/* Draggable item styles */
.draggableItem {
  --transform: none;
  --opacity: 1;
  --z-index: 0;
  
  transform: var(--transform);
  opacity: var(--opacity);
  z-index: var(--z-index);
  transition: transform 0.2s ease, opacity 0.2s ease;
  will-change: transform, opacity;
}

/* Apply these styles when item is being dragged */
.draggableItem.dragging {
  opacity: 0.5;
  z-index: 1;
}

.dragHandle {
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: grab;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.dragHandle:hover {
  background-color: rgba(243, 244, 246, 0.5);
}

.dragHandle:active {
  cursor: grabbing;
}

.dragHandleIcon {
  height: 1.25rem;
  width: 1.25rem;
  color: #9ca3af;
}
