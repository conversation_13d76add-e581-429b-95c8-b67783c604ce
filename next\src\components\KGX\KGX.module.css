.clickable {
  cursor: pointer;
}

.contentContainer {
  position: relative;
  height: 4rem;
  overflow: hidden;
}

/* Base slide styles */
.contentSlide {
  position: absolute;
  width: 100%;
  transition: transform 0.5s ease-out;
  will-change: transform;
}

/* Generate transform classes for up to 20 slides */
.slide0 {
  transform: translateY(0);
}
.slide1 {
  transform: translateY(-4rem);
}
.slide2 {
  transform: translateY(-8rem);
}
.slide3 {
  transform: translateY(-12rem);
}
.slide4 {
  transform: translateY(-16rem);
}
.slide5 {
  transform: translateY(-20rem);
}
.slide6 {
  transform: translateY(-24rem);
}
.slide7 {
  transform: translateY(-28rem);
}
.slide8 {
  transform: translateY(-32rem);
}
.slide9 {
  transform: translateY(-36rem);
}
.slide10 {
  transform: translateY(-40rem);
}
.slide11 {
  transform: translateY(-44rem);
}
.slide12 {
  transform: translateY(-48rem);
}
.slide13 {
  transform: translateY(-52rem);
}
.slide14 {
  transform: translateY(-56rem);
}
.slide15 {
  transform: translateY(-60rem);
}
.slide16 {
  transform: translateY(-64rem);
}
.slide17 {
  transform: translateY(-68rem);
}
.slide18 {
  transform: translateY(-72rem);
}
.slide19 {
  transform: translateY(-76rem);
}

/* Draggable item styles */
.draggableItem {
  --transform: none;
  --opacity: 1;
  --z-index: 0;
  
  transform: var(--transform);
  opacity: var(--opacity);
  z-index: var(--z-index);
  transition: transform 0.2s ease, opacity 0.2s ease;
  will-change: transform, opacity;
}

/* Apply these styles when item is being dragged */
.draggableItem.dragging {
  opacity: 0.5;
  z-index: 1;
}

.dragHandle {
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: grab;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.dragHandle:hover {
  background-color: rgba(243, 244, 246, 0.5);
}

.dragHandle:active {
  cursor: grabbing;
}

.dragHandleIcon {
  height: 1.25rem;
  width: 1.25rem;
  color: #9ca3af;
}
