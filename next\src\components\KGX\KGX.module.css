.clickable {
  cursor: pointer;
}

.contentContainer {
  position: relative !important;
  height: 4rem !important;
  overflow: visible !important;
  background-color: rgba(0, 255, 0, 0.3) !important;
  border: 2px solid green !important;
}

/* Base slide styles */
.contentSlide {
  position: relative !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: auto !important;
  transition: transform 0.5s ease-out !important;
  will-change: transform !important;
  z-index: 10 !important;
  opacity: 1 !important;
  display: block !important;
  background-color: rgba(255, 0, 0, 0.5) !important;
  border: 3px solid red !important;
  color: black !important;
  font-size: 16px !important;
}

/* Generate transform classes for up to 20 slides */
/* All slides should be positioned at translateY(0) to be visible in the container */
.slide0 {
  transform: translateY(0);
  opacity: 1;
  z-index: 2;
}
.slide1 {
  transform: translateY(0);
}
.slide2 {
  transform: translateY(0);
}
.slide3 {
  transform: translateY(0);
}
.slide4 {
  transform: translateY(0);
}
.slide5 {
  transform: translateY(0);
}
.slide6 {
  transform: translateY(0);
}
.slide7 {
  transform: translateY(0);
}
.slide8 {
  transform: translateY(0);
}
.slide9 {
  transform: translateY(0);
}
.slide10 {
  transform: translateY(0);
}
.slide11 {
  transform: translateY(0);
}
.slide12 {
  transform: translateY(0);
}
.slide13 {
  transform: translateY(0);
}
.slide14 {
  transform: translateY(0);
}
.slide15 {
  transform: translateY(0);
}
.slide16 {
  transform: translateY(0);
}
.slide17 {
  transform: translateY(0);
}
.slide18 {
  transform: translateY(0);
}
.slide19 {
  transform: translateY(0);
}

/* Draggable item styles */
.draggableItem {
  --transform: none;
  --opacity: 1;
  --z-index: 0;
  
  transform: var(--transform);
  opacity: var(--opacity);
  z-index: var(--z-index);
  transition: transform 0.2s ease, opacity 0.2s ease;
  will-change: transform, opacity;
}

/* Apply these styles when item is being dragged */
.draggableItem.dragging {
  opacity: 0.5;
  z-index: 1;
}

.dragHandle {
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: grab;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.dragHandle:hover {
  background-color: rgba(243, 244, 246, 0.5);
}

.dragHandle:active {
  cursor: grabbing;
}

.dragHandleIcon {
  height: 1.25rem;
  width: 1.25rem;
  color: #9ca3af;
}
