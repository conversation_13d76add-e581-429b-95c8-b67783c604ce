import classNames from 'classnames'
import clsx from 'clsx'
import styles from '~/src/components/BidAskGrid/BidAskGrid.module.scss'
import { useGetMarketStatus } from '~/src/utils/market-status.util'

export const MarketStatus = () => {
  const { data, statusString, timeToNextStatusString } = useGetMarketStatus()
  const classes = classNames(
    styles.upper,
    { [styles.up]: data?.GetMarketStatus?.status === 'OPEN' },
    { [styles.down]: data?.GetMarketStatus?.status === 'CLOSED' },
  )
  return (
    <div className={classNames(styles.wrap, 'block')}>
      <div className={clsx(styles.market, 'block')}>
        <h4 className={classNames(classes)}>{statusString} </h4>
        <div>
          <small className="text-black">({timeToNextStatusString})</small>
        </div>
      </div>
    </div>
  )
}

export default MarketStatus
