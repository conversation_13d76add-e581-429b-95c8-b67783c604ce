kind: ConfigMap
apiVersion: v1
metadata:
  name: nextjs-varnish
data:
  default.vcl: |-
    {{- $file := (.Files.Get "vcl/main.vcl") -}}
    {{- $rendered := tpl $file . -}}
    {{ $rendered | nindent 4 }}

  # tables
  redirects.dict: |-
    {{- (.Files.Get "vcl/recvs/redirects.dict") | nindent 4 }}
  # recvs
  cache_bypasses.vcl: |-
    {{- $file := (.Files.Get "vcl/recvs/cache_bypasses.vcl") -}}
    {{- $rendered := tpl $file . -}}
    {{ $rendered | nindent 4 }}
  redirects.vcl: |-
    {{- $file := (.Files.Get "vcl/recvs/redirects.vcl") -}}
    {{- $rendered := tpl $file . -}}
    {{ $rendered | nindent 4 }}
  # subroutines
  init.vcl: |-
    {{- (.Files.Get "vcl/init.vcl") | nindent 4 }}
  # input file contains variable so we have to parse it using `tpl` directive
  recv.vcl: |-
    {{- $file := (.Files.Get "vcl/recv.vcl") -}}
    {{- $rendered := tpl $file . -}}
    {{ $rendered | nindent 4 }}
  synth.vcl: |-
    {{- (.Files.Get "vcl/synth.vcl") | nindent 4 }}
  deliver.vcl: |-
    {{- (.Files.Get "vcl/deliver.vcl") | nindent 4 }}
