import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import clsx from 'clsx'
import { ReactNode, useEffect, useState } from 'react'
import { FaGripVertical } from 'react-icons/fa'

interface DraggableRowProps
  extends Omit<
    React.HTMLAttributes<HTMLTableRowElement>,
    'onDragStart' | 'onDragOver' | 'onDrop'
  > {
  id: string
  children: ReactNode
  className?: string
  style?: React.CSSProperties
  onDragStart?: (e: React.DragEvent<HTMLTableRowElement>) => void
  onDragOver?: (e: React.DragEvent<HTMLTableRowElement>) => void
  onDrop?: (e: React.DragEvent<HTMLTableRowElement>) => void
}

export const DraggableRow = ({
  id,
  children,
  className,
  style: propStyle,
  ...rest
}: DraggableRowProps) => {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id,
    // Disable animations on initial render to prevent hydration mismatch
    animateLayoutChanges: () => isMounted,
  })

  const style: React.CSSProperties = {
    transform: CSS.Translate.toString(transform),
    transition: transition || undefined,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1 : 'auto',
    ...propStyle,
  }

  return (
    <tr
      ref={setNodeRef}
      style={style}
      className={clsx(
        className,
        'hover:bg-gray-50',
        isDragging && 'opacity-50',
      )}
      {...attributes}
      {...rest}
    >
      {children}
      <td className="w-8 px-2">
        <div
          {...attributes}
          {...listeners}
          className="flex h-full w-6 cursor-grab items-center justify-center text-gray-400 hover:text-gray-600 active:cursor-grabbing"
          aria-label="Drag to reorder"
        >
          <FaGripVertical className="h-4 w-4" />
        </div>
      </td>
    </tr>
  )
}
