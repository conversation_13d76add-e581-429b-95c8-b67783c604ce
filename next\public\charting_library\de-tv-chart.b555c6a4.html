<!DOCTYPE html><html lang="de" dir="ltr"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=Edge"><script>window===window.parent&&(location.href="about:blank")</script><script defer="defer" crossorigin="anonymous" src="bundles/runtime.cc2522d6a97ad4920031.js"></script><script defer="defer" crossorigin="anonymous" src="bundles/vendors.0cfb111426f49f90fa86.js"></script><script defer="defer" crossorigin="anonymous" src="bundles/library.3afef79a8ae8c7a5618e.js"></script><link type="text/css" href="bundles/library.c9ed08366453322ce64f.css" rel="stylesheet"></head><body class="chart-page unselectable on-widget"><div class="loading-indicator" id="loading-indicator"></div><script>var JSServer={},__initialEnabledFeaturesets=["charting_library"]</script><script>!function(){window.urlParams=function(){function n(n){return decodeURIComponent(n.replace(t," ")).replace(/<\/?[^>]+(>|$)/g,"")}for(var e,t=/\+/g,r=/([^&=]+)=?([^&]*)/g,i=function(){var n=location.href,e=n.indexOf("#");if(0<=e)return n.substring(e+1);throw"Unexpected use of this page"}(),o={};e=r.exec(i);)o[n(e[1])]=n(e[2]);var s,a=window.parent[o.uid],l=["datafeed","customFormatters","brokerFactory","save_load_adapter"];for(s in a)-1===l.indexOf(s)&&(o[s]=JSON.stringify(a[s]));return o}(),window.locale=urlParams.locale,window.language=urlParams.locale,window.addCustomCSSFile=function(n){var e=document.createElement("link");e.setAttribute("type","text/css"),e.setAttribute("rel","stylesheet"),e.setAttribute("href",n),document.body.appendChild(e)},urlParams.customCSS&&window.addCustomCSSFile(urlParams.customCSS);var n={};if("string"==typeof urlParams.loading_screen)try{n=JSON.parse(urlParams.loading_screen)}catch(n){}var e=document.getElementById("loading-indicator");n.backgroundColor&&(e.style="background-color: "+n.backgroundColor),function(){"use strict";var n,e,t;i="\n/* Thanks to google guys for the original <paper-spinner> =)\n * https://github.com/PolymerElements/paper-spinner */\n.tv-spinner {\n  display: none;\n  position: absolute;\n  width: 1em;\n  height: 1em;\n  top: calc(50% - 0.5em);\n  left: calc(50% - 0.5em);\n  margin: 0 auto;\n  color: #37a6ef;\n  animation: tv-spinner__container-rotate 0.9s linear infinite;\n  will-change: transform;\n  /* The spinner does not have any contents that would have to be\n\t * flipped if the direction changes. Always use ltr so that the\n\t * style works out correctly in both cases. */\n  direction: ltr;\n}\n.tv-spinner--size_mini {\n  font-size: 16px;\n}\n.tv-spinner--size_medium {\n  font-size: 32px;\n}\n.tv-spinner--size_large {\n  font-size: 56px;\n}\n.tv-spinner--size_mini .tv-spinner__width_element:after {\n  border-width: 2px;\n}\n.tv-spinner--size_medium .tv-spinner__width_element:after {\n  border-width: 3px;\n}\n.tv-spinner--size_large .tv-spinner__width_element:after {\n  border-width: 4px;\n}\n.tv-spinner--shown {\n  display: block;\n}\n.tv-spinner__spinner-layer {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  white-space: nowrap;\n  color: currentColor;\n  transform: rotate(90deg);\n  /**\n\t\t * Patch the gap that appear between the two adjacent div.circle-clipper while the\n\t\t * spinner is rotating (appears on Chrome 50, Safari 9.1.1, and Edge).\n\t\t */\n}\n.tv-spinner__spinner-layer::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  border-width: 0.07em;\n  border-radius: 50%;\n  left: 45%;\n  width: 10%;\n  border-top-style: solid;\n}\n.tv-spinner__background {\n  display: inline-block;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n.tv-spinner__background::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  left: 0;\n  border-radius: 50%;\n  bottom: 0;\n  width: 100%;\n  border-color: rgba(135, 151, 165, 0.2);\n  border-style: solid;\n}\n.tv-spinner__circle-clipper {\n  display: inline-block;\n  position: relative;\n  width: 50%;\n  height: 100%;\n  overflow: hidden;\n}\n.tv-spinner__circle-clipper::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  border-radius: 50%;\n  bottom: 0;\n  width: 200%;\n  border-style: solid;\n  border-bottom-color: transparent;\n  animation-duration: 1.4s;\n  animation-timing-function: cubic-bezier(0.36, 0, 0.37, 0.99);\n  animation-iteration-count: 1;\n  will-change: transform;\n}\n.tv-spinner__circle-clipper--left::after {\n  left: 0;\n  border-right-color: transparent;\n  transform: rotate(0deg);\n  animation-name: tv-spinner__left-spin;\n}\n.tv-spinner__circle-clipper--right::after {\n  left: -100%;\n  border-left-color: transparent;\n  transform: rotate(-124deg);\n  animation-name: tv-spinner__right-spin;\n}\n@keyframes tv-spinner__container-rotate {\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@keyframes tv-spinner__left-spin {\n  0% {\n    transform: rotate(130deg);\n  }\n  to {\n    transform: rotate(0deg);\n  }\n}\n@keyframes tv-spinner__right-spin {\n  0% {\n    transform: rotate(-130deg);\n  }\n  to {\n    transform: rotate(-124deg);\n  }\n}\n",t=(n=void 0===n?{}:n).insertAt,"undefined"!=typeof document&&(e=document.head||document.getElementsByTagName("head")[0],(n=document.createElement("style")).type="text/css","top"===t&&e.firstChild?e.insertBefore(n,e.firstChild):e.appendChild(n),n.styleSheet?n.styleSheet.cssText=i:n.appendChild(document.createTextNode(i)));var s,a=new WeakMap;(i=s=s||{})[i.Element=1]="Element",i[i.Document=9]="Document";var r=function(){var n,e,t=(e=document.documentElement,(n=a?a.get(e):n)||((n=e.ownerDocument.createRange()).selectNodeContents(e),a&&a.set(e,n)),n.createContextualFragment('\n\t\t<div class="tv-spinner" role="progressbar">\n\t\t\t<div class="tv-spinner__spinner-layer">\n\t\t\t\t<div class="tv-spinner__background tv-spinner__width_element"></div>\n\t\t\t\t<div class="tv-spinner__circle-clipper tv-spinner__width_element tv-spinner__circle-clipper--left"></div>\x3c!--\n\t\t\t\t--\x3e<div class="tv-spinner__circle-clipper tv-spinner__width_element tv-spinner__circle-clipper--right"></div>\n\t\t\t</div>\n\t\t</div>\n\t'));if("firstElementChild"in t)r=t.firstElementChild;else for(var r=null,i=0;i<t.childNodes.length;i++){var o=t.childNodes[i];if(o.nodeType===s.Element){r=o;break}}return null!==r&&t.removeChild(r),r}(),i=(o.prototype.spin=function(n){return this._el.classList.add("tv-spinner--shown"),void 0===this._container&&(void 0!==(this._container=n)&&n.appendChild(this._el)),this._shown=!0,this},o.prototype.stop=function(n){return n&&void 0!==this._container&&this._container.removeChild(this._el),this._el.classList.remove("tv-spinner--shown"),this._shown=!1,this},o.prototype.setStyle=function(t){var r=this;return Object.keys(t).forEach(function(n){var e=t[n];void 0!==e&&r._el.style.setProperty(n,e)}),this},o.prototype.setSize=function(n){n=void 0!==n?"tv-spinner--size_"+n:"";return this._el.className="tv-spinner "+n+" "+(this._shown?"tv-spinner--shown":""),this},o.prototype.getEl=function(){return this._el},o.prototype.destroy=function(){this.stop(),delete this._el,delete this._container},o);function o(n){this._shown=!1,this._el=r.cloneNode(!0),this.setSize(n||"large")}window.Spinner=i}();var t=n.foregroundColor||"auto";(t=new Spinner("large").setStyle({color:t,zIndex:String(2e9)})).getEl().classList.add("spinner"),t.spin(e)}()</script><script>var _tv_languages=_tv_languages||{};_tv_languages.de={Save:"Speichern",Show:"Anzeigen",Delete:"Löschen",Cancel:"Abbrechen",Yes:"Ja",No:"Nein",Price:"Preis",Currency:"Währung",Comment:"Kommentar",Interval:"Intervall",Time:"Zeit",Ticker:"Ticker, Name",Search:"Suche","Learn more":"Mehr erfahren",Date:"Datum",Source:"Quelle",Description:"Beschreibung",Settings:"Einstellungen",Width:"Breite",Background:"Hintergrund",Border:"Rahmen",Apply:"Anwenden",Exchange:"Börse",Timezone:"Zeitzone","Color Theme":"Farb-Design","Bar's Style":"Balken Darstellung",Bars:"Balken",Candles:"Kerzen","Heikin Ashi":"HeikinAshi",Line:"Linie",Area:"Fläche",Grid:"Gitter",Add:"Hinzufügen",Regular:"Regulär",Sector:"Sektor",Industry:"Branche",Remove:"Entfernen",January:"Januar",February:"Februar",March:"März",May:"Mai",June:"Juni",July:"Juli",December:"Dezember","Indicators & Strategies":"Indikatoren & Strategien",Contracts:"Verträge",Prices:"Preise",Change:"Änderung","Hong Kong":"Hongkong",forex:"Devisen",Type:"Typ",Error:"Fehler","Invalid Symbol":"Ungültiges Symbol",More:"Mehr",Luxembourg:"Luxemburg","New Zealand":"Neuseeland",Qatar:"Katar",Singapore:"Singapur","At close":"bei Schluß",Holiday:"Urlaub / Feiertag","Click to set a point":"Klicken Sie, um einen Punkt zu setzen","Move the point to position the anchor then tap to place":"Bewegen Sie den Punkt, um den Anker zu positionieren, und tappen Sie dann um ihn zu platzieren.","Press and hold {0} while zooming to maintain the chart position":"Drücken und halten Sie {0} beim Zoomen, um die Chartposition bei zu behalten",Baseline:"Grundlinie",Quantity:"Anzahl",Reverse:"INVERT","N/A":"Nicht verfügbar",Compare:"Vergleichen","Recently used":"Kürzlich verwendet",month:"Monat",month_plural:"Monate","Copied to clipboard":"In die Zwischenablage kopiert",Hide:"Verbergen",m_interval_short:"m",h_interval_short:"Std.",D_interval_short:"D",W_interval_short:"W",M_interval_short:"M","Remove from favorites":"Aus Favoriten entfernen","Add to favorites":"Zu Favoriten hinzufügen","Add Text Note for {0}":"Textnotiz für {0} hinzufügen","Time Interval":"Zeitinterval","Are you sure?":"Sind Sie sicher?",Copy:"Kopieren",Drawings:"Zeichnungen",Indicators:"Indikatoren","Price format is invalid.":"Preisformat ist ungültig.","Risk/Reward":"Chance/Risiko","Number format is invalid.":"Ungültiges Zahlenformat","Specified value is less that the instrument minimum.":"Der festgelegte Wert ist kleiner als das Minimum des Instruments.","Specified value is more than the instrument maximum.":"Der festgelegte Wert ist größer als das Maximum des Instruments.",Risk:"Risiko",Loading:"Wird geladen","Price Line":"Preislinie",Cairo:"Kairo","Mexico City":"Mexiko City",Kolkata:"Kalkutta",Muscat:"Muskat",Riyadh:"Riad",Tokyo:"Tokio",Athens:"Athen",Belgrade:"Belgrad",Brussels:"Brüssel",Lisbon:"Lissabon",Moscow:"Moskau",Rome:"Rom",Warsaw:"Warschau",Zurich:"Zürich","Chatham Islands":"Chatham Inseln",Coordinates:"Koordinaten","Events & Alerts":"Ereignisse & Alarme",Events:"Ereignisse",Inputs:"Eingaben",Properties:"Eigenschaften",Scales:"Skalierungen","Source Code":"Quellcode",Style:"Stil","Timezone/Sessions":"Zeitzone/Handelszeiten",Visibility:"Sichtbarkeit","Box size assignment method":"Zuweisungsmethode der Boxgrösse","Color bars based on previous close":"Balken gemäß vorherigem Schlußkurs färben.",Borders:"Rahmen",Wick:"Docht","HLC bars":"HLC-Balken","Price source":"Preisquelle","Show real prices on price scale (instead of Heikin-Ashi price)":"Reale Preise auf der Preisachse anzeigen (anstatt der Heikin-Ashi-Preise)","Projection up color":"Farbe für Projektion nach oben","Projection down color":"Farbe für Projektion nach unten",Fill:"Füllen","Up color":"Aufwärts Farbe","Down color":"Abwärts Farbe",Traditional:"Traditionell","Box size":"Boxgröße","Number of line":"Linienzahl","ATR length":"ATR-Länge","Reversal amount":"Umkehrbetrag","Phantom bars":"Phantom Balken","One step back building":"One Step Back Aufbau",Wicks:"Dochte","You cannot see this pivot timeframe on this resolution":"Sie können dieses Pivot-Zeitfenster nicht mit dieser Auflösung sehen.","Accumulation/Distribution_study":"Kumulierung / Distribution","Accumulative Swing Index_study":"Accumulative Swing Index","Advance/Decline_study":"Anstieg/Rückgang","Arnaud Legoux Moving Average_study":"Arnaud Legoux Moving Average",Aroon_study:"Aroon",ASI_study:"ASI","Average Directional Index_study":"Average Directional Index","Average True Range_study":"Average True Range","Awesome Oscillator_study":"Awesome Oscillator","Balance of Power_study":"Balance of Power","Bollinger Bands %B_study":"Bollinger Bands %B","Bollinger Bands Width_study":"Bollinger Bands Width","Bollinger Bands_study":"Bollinger Bands","Chaikin Money Flow_study":"Chaikin Money Flow","Chaikin Oscillator_study":"Chaikin-Oszillator","Chande Kroll Stop_study":"Chande Kroll Stop","Chande Momentum Oscillator_study":"Chande Momentum Oscillator","Chop Zone_study":"Chop Zone","Choppiness Index_study":"Choppiness Index","Commodity Channel Index_study":"Commodity Channel Index","Connors RSI_study":"Connors RSI","Coppock Curve_study":"Coppock Curve","Correlation Coefficient_study":"Korrelations-Koeffizient",CRSI_study:"CRSI","Detrended Price Oscillator_study":"Detrended Price Oscillator","Directional Movement_study":"Directional Movement","Donchian Channels_study":"Donchian Channels","Double EMA_study":"Double EMA","Ease Of Movement_study":"Ease Of Movement","Elder's Force Index_study":"Elder's Force Index","EMA Cross_study":"EMA Cross",Envelopes_study:"Umrandungen","Fisher Transform_study":"Fisher Transform","Fixed Range_study":"Fixed Range","Historical Volatility_study":"Historische Volatilität","Hull Moving Average_study":"Hull Moving Average","Ichimoku Cloud_study":"Ichimoku Cloud","Keltner Channels_study":"Keltner Channels","Klinger Oscillator_study":"Klinger Oszillator","Know Sure Thing_study":"Know-Sure-Thing","Least Squares Moving Average_study":"Least-Squares Moving Average","Linear Regression Curve_study":"Linear Regression-Kurve","MA Cross_study":"MA Cross","MA with EMA Cross_study":"MA mit EMA Cross","MA/EMA Cross_study":"MA/EMA Cross",MACD_study:"MACD","Mass Index_study":"Mass-Index","McGinley Dynamic_study":"McGinley-Dynamik",Momentum_study:"Momentum","Money Flow_study":"Money Flow","Moving Average Channel_study":"Moving Average Channel","Moving Average Exponential_study":"Gleitender Durchschnitt exponentiell","Moving Average Weighted_study":"Gleitender Durchschnitt gewichtet","Moving Average_study":"Gleitender Durchschnitt","Net Volume_study":"Nettovolumen","On Balance Volume_study":"On Balance Volume","Parabolic SAR_study":"Parabolic SAR","Pivot Points Standard_study":"Pivot Points Standard","Price Channel_study":"Price Channel","Price Oscillator_study":"Price-Oszillator","Price Volume Trend_study":"Price Volume-Trend","Rate Of Change_study":"Rate Of Change","Relative Strength Index_study":"Relative Strength Index","Relative Vigor Index_study":"Relative Vigor Index","Relative Volatility Index_study":"Relative Volatility Index","Session Volume_study":"Session Volume","Session Volume HD_study":"Session Volume HD","SMI Ergodic Indicator/Oscillator_study":"SMI Ergodic Indikator/Oszillator","Smoothed Moving Average_study":"Smoothed Moving Average","Stochastic RSI_study":"Stochastic RSI",Stochastic_study:"Stochastik","Triple EMA_study":"Triple EMA",TRIX_study:"TRIX","True Strength Indicator_study":"True Strength-Indikator","Ultimate Oscillator_study":"Ultimate-Oszillator","Visible Range_study":"Sichtbare Spanne","Volume Oscillator_study":"Volumen Oszillator",Volume_study:"Volumen","Vortex Indicator_study":"Vortex-Indikator",VWAP_study:"VWAP",VWMA_study:"VWMA","Williams %R_study":"Williams %R","Williams Alligator_study":"Williams Alligator","Williams Fractal_study":"Williams Fractal","Zig Zag_study":"Zig Zag","Plots Background_study":"Plots Hintergrund",SuperTrend_study:"SuperTrend","Average Price_study":"Durchschnittlicher Preis","Typical Price_study":"Typischer Preis","Median Price_study":"Median Preis","Money Flow Index_study":"Money Flow Index","Moving Average Double_study":"Moving Average Double","Moving Average Triple_study":"Moving Average Triple","Moving Average Adaptive_study":"Moving Average Adaptive","Moving Average Hamming_study":"Moving Average Hamming","Moving Average Modified_study":"Moving Average Modified","Moving Average Multiple_study":"Moving Average Multiple","Linear Regression Slope_study":"Linear Regression Slope","Standard Error_study":"Standard Error","Standard Error Bands_study":"Standard Error Bands","Correlation - Log_study":"Correlation - Log","Standard Deviation_study":"Standard Deviation","Chaikin Volatility_study":"Chaikin Volatility","Volatility Close-to-Close_study":"Volatility Close-to-Close","Volatility Zero Trend Close-to-Close_study":"Volatility Zero Trend Close-to-Close","Volatility O-H-L-C_study":"Volatility O-H-L-C","Volatility Index_study":"Volatility Index","Trend Strength Index_study":"Trend Strength Index","Majority Rule_study":"Majority Rule",Length_input:"Länge",Plot_input:"Plot",Zero_input:"Null",Signal_input:"Signal",Long_input:"Long",Short_input:"Short",UpperLimit_input:"OberesLimit",LowerLimit_input:"UntereBegrenzung",Offset_input:"Offset",length_input:"Länge",mult_input:"mult",short_input:"short",long_input:"long",Limit_input:"Limit",Move_input:"Bewegung",Value_input:"Wert",Method_input:"Method","Accumulation/Distribution_input":"Akkumulation/Distribution",ADR_B_input:"ADR_B","Equality Line_input":"Gleichheitslinie","Window Size_input":"Fenstergröße",Sigma_input:"Sigma","Aroon Up_input":"Aroon Up","Aroon Down_input":"Aroon Down",Upper_input:"Oberes",Lower_input:"Unteres",ADX_input:"ADX","ADX Smoothing_input":"ADX Glättung","DI Length_input":"DI-Länge",Smoothing_input:"Glättung",ATR_input:"ATR",Growing_input:"Wachsend",Falling_input:"Fallend","Color 0_input":"Farbe 0","Color 1_input":"Farbe 1",Source_input:"Quelle",StdDev_input:"StdDev",Basis_input:"Basis",Median_input:"Median","Bollinger Bands %B_input":"Bollinger Bands %B",Overbought_input:"Überkauft",Oversold_input:"Überverkauft","Bollinger Bands Width_input":"Bollinger Bands-Breite","RSI Length_input":"RSI Länge","UpDown Length_input":"UpDown Length","ROC Length_input":"ROC Length",MF_input:"MF",resolution_input:"Auflösung","Fast Length_input":"Schnelle Periode","Slow Length_input":"Langsame Periode","Chaikin Oscillator_input":"Chaikin-Oszillator",P_input:"P",X_input:"X",Q_input:"Q",p_input:"p",x_input:"x",q_input:"q",Price_input:"Kurs","Chande MO_input":"Chande MO","Zero Line_input":"Nulllinie","Color 2_input":"Farbe 2","Color 3_input":"Farbe 3","Color 4_input":"Farbe 4","Color 5_input":"Farbe 5","Color 6_input":"Farbe 6","Color 7_input":"Farbe 7","Color 8_input":"Farbe 8",CHOP_input:"CHOP","Upper Band_input":"Oberes Band","Lower Band_input":"Unteres Band",CCI_input:"CCI","WMA Length_input":"WMA Länge","Long RoC Length_input":"Long RoC Length","Short RoC Length_input":"Short RoC Length",sym_input:"sym",Symbol_input:"Symbol",Correlation_input:"Korrelation",Period_input:"Zeitraum",Centered_input:"Zentriert","Detrended Price Oscillator_input":"Detrended Price-Oszillator",isCentered_input:"isCentered",DPO_input:"DPO","ADX smoothing_input":"ADX Glättung","+DI_input":"+DI","-DI_input":"-DI",DEMA_input:"DEMA",Divisor_input:"Divisor",EOM_input:"EOM","Elder's Force Index_input":"Elder's Force-Index",Percent_input:"Prozent",Exponential_input:"Exponentiell",Average_input:"Durchschnitt","Upper Percentage_input":"Höhere Prozentzahl","Lower Percentage_input":"Niedrigere Prozentzahl",Fisher_input:"Fisher",Trigger_input:"Auslöser",Level_input:"Level",HV_input:"HV","Hull MA_input":"Hull MA","Conversion Line Periods_input":"Conversion Line Perioden","Base Line Periods_input":"Base Line Periods","Lagging Span 2 Periods_input":"Lagging Span 2 Periods",Displacement_input:"Verschiebung","Conversion Line_input":"Conversion Line","Base Line_input":"Grundlinie","Lagging Span_input":"Lagging Span","Lead 1_input":"Lead 1","Lead 2_input":"Lead 2","yay Color 0_input":"yay Farbe 0","yay Color 1_input":"yay Farbe 1",Multiplier_input:"Multiplikator","Bands style_input":"Bänder-Stil",Middle_input:"Mitte",useTrueRange_input:"useTrueRange",ROCLen1_input:"ROCLen1",ROCLen2_input:"ROCLen2",ROCLen3_input:"ROCLen3",ROCLen4_input:"ROCLen4",SMALen1_input:"SMALen1",SMALen2_input:"SMALen2",SMALen3_input:"SMALen3",SMALen4_input:"SMALen4",SigLen_input:"SigLen",KST_input:"KST",Sig_input:"Sig",roclen1_input:"roclen1",roclen2_input:"roclen2",roclen3_input:"roclen3",roclen4_input:"roclen4",smalen1_input:"smalen1",smalen2_input:"smalen2",smalen3_input:"smalen3",smalen4_input:"smalen4",siglen_input:"siglen","Upper Deviation_input":"Obere Abweichung","Lower Deviation_input":"Untere Abweichung","Use Upper Deviation_input":"Benutze obere Abweichung","Use Lower Deviation_input":"Benutze untere Abweichung",Count_input:"Anzahl",Crosses_input:"Kreuzt",MOM_input:"MOM",MA_input:"MA","Length EMA_input":"Länge EMA","Length MA_input":"Länge MA","Fast length_input":"Fast length","Slow length_input":"Slow length","Signal smoothing_input":"Signalglättung","Simple ma(oscillator)_input":"Simple ma(Oszillator)","Simple ma(signal line)_input":"Simple ma(Signallinie)",Histogram_input:"Histogramm",MACD_input:"MACD",fastLength_input:"fastLength",slowLength_input:"slowLength",signalLength_input:"signalLänge",NV_input:"NV",OnBalanceVolume_input:"OnBalanceVolumen",Start_input:"Start",Increment_input:"Schrittweite","Max value_input":"Maximalwert",ParabolicSAR_input:"ParabolicSAR",start_input:"Start",increment_input:"Zuwachs",maximum_input:"Maximum","Short length_input":"Short length","Long length_input":"Long length",OSC_input:"OSC",shortlen_input:"shortlen",longlen_input:"longlen",PVT_input:"PVT",ROC_input:"ROC",RSI_input:"RSI",RVGI_input:"RVGI",RVI_input:"RVI","Long period_input":"Long Periode","Short period_input":"Kurzer Zeitraum","Signal line period_input":"Singnallinienperiode",SMI_input:"SMI","SMI Ergodic Oscillator_input":"SMI Ergodic-Oszillator",Indicator_input:"Indikator",Oscillator_input:"Oszillator",K_input:"K",D_input:"D",smoothK_input:"smoothK",smoothD_input:"smoothD","%K_input":"%K","%D_input":"%D","Stochastic Length_input":"Stochastische Länge","RSI Source_input":"RSI Quelle",lengthRSI_input:"LängeRSI",lengthStoch_input:"LängeStoch",TRIX_input:"TRIX",TEMA_input:"TEMA","Long Length_input":"Long Length","Short Length_input":"Short Length","Signal Length_input":"Signallänge",Length1_input:"Length1",Length2_input:"Length2",Length3_input:"Length3",length7_input:"Länge7",length14_input:"Länge14",length28_input:"Länge28",UO_input:"UO",VWMA_input:"VWMA",len_input:"len","VI +_input":"VI +","VI -_input":"VI -","%R_input":"%R","Jaw Length_input":"Jaw Length","Teeth Length_input":"Teeth Length","Lips Length_input":"Lips Length",Jaw_input:"Jaw",Teeth_input:"Teeth",Lips_input:"Lips",jawLength_input:"jawLength",teethLength_input:"teethLength",lipsLength_input:"lipsLength","Down fractals_input":"Down fractals","Up fractals_input":"Up fractals",Periods_input:"Zeiträume",Shapes_input:"Formen","show MA_input":"MA anzeigen","MA Length_input":"MA Länge","Color based on previous close_input":"Farben basierend auf vorherigem Schlusskurs","Rows Layout_input":"Zeilenlayout","Row Size_input":"Zeilengröße",Volume_input:"Volumen","Value Area volume_input":"Value Area-Volumen","Extend POC Right_input":"Extend POC Right","Value Area Volume_input":"Value Area Volumen",Placement_input:"Platzierung",POC_input:"POC","Developing Poc_input":"Entwicklung des Poc","Up Volume_input":"Aufwärts-Volumen","Down Volume_input":"Abwärts-Volumen","Value Area_input":"Value Area","Histogram Box_input":"Histogram-Box","Value Area Up_input":"Value  Area aufwärts","Value Area Down_input":"Value Area abwärts","Number Of Rows_input":"Zeilenanzahl","Ticks Per Row_input":"Ticks pro Zeile","Up/Down_input":"Auf/Ab",Total_input:"Gesamt","Deviation (%)_input":"Abweichung (%)",Depth_input:"Tiefe","Extend to last bar_input":"Erweitern bis zur letzten Bar",Simple_input:"Einfach",Weighted_input:"Gewichtet","Wilder's Smoothing_input":"Wilder'sche Glättung","1st Period_input":"Erste Periode","2nd Period_input":"2te Periode","3rd Period_input":"3te Periode","4th Period_input":"4te Periode","5th Period_input":"5te Periode","6th Period_input":"6te Periode","Rate of Change Lookback_input":"Rate of Change zurückliegende Perioden","Instrument 1_input":"Instrument 1","Instrument 2_input":"Instrument 2","Rolling Period_input":"Rollende Periode","Standard Errors_input":"Standart-Abweichung","Averaging Periods_input":"Durchschnittsperioden","Days Per Year_input":"Tage pro Jahr","Market Closed Percentage_input":"Markt-Close prozentual","ATR Mult_input":"ATR Mult",VWAP_input:"VWAP","Anchor Period_input":"Verankerungszeitraum",Session_input:"Session",Week_input:"Woche",Month_input:"Monat",Year_input:"Jahr",Decade_input:"Jahrzehnt",Century_input:"Jahrhundert","Go to":"Gehe zu",Percent:"Prozent","Symbol Info":"Symboinfo","Image URL":"Bild URL","Save image":"Bild speichern","Sorry, the Copy Link button doesn't work in your browser. Please select the link and copy it manually.":'Entschuldigung, der Knopf "Link-Adresse kopieren" funktioniert nicht in Ihrem Browser. Bitte markieren Sie das Link und kopieren Sie es manuell.',"Rename...":"Umbenennen...","Save all charts for all symbols and intervals on your layout":"Speichern Sie alle Charts für alle Symbole und Intervalle in Ihrem Layout","Load Chart Layout":"Chart-Layout laden",minutes_interval:"Minuten",hours_interval:"Stunden",days_interval:"Tage",weeks_interval:"Wochen",months_interval:"Monate","Add custom time interval":"Individuelles Zeit Interval hinzufügen","Toggle Auto Scale":"Auf automatische Skalierung umschalten","Toggle Log Scale":"Auf logarithmische Skalierung umschalten","Toggle Percentage Scale":"Prozentskala umschalten","Take a snapshot":"Snapshot erstellen",ext:"verl.","Extended Hours is available only for intraday charts":"Verlängerte Handelszeiten sind nur für Intraday-Charts verfügbar","Toggle Percentage":"Auf Prozent umschalten",log_scale:"log",auto_scale:"auto","Toggle Maximize Chart":"Auf maximierten Chart umschalten",adj_adjustments:"Anp.","Adjust data for dividends":"Daten für Dividenden anpassen","Data is adjusted for dividends only":"Die Daten sind ausschließlich für Dividenden angepasst.","Data is adjusted for splits only":"Die Daten werden nur für Splits angepasst.","Date Range":"Datumsbereich","Go to...":"Gehe zu...","Weak Magnet":"Leichter Magnetmodus","Strong Magnet":"Starker Magnetmodus",Cursors:"Mauszeiger","Trend Line Tools":"Trendlinien Tools","Gann and Fibonacci Tools":"Gann und Fibonacci Tools","Geometric Shapes":"Geometrische Formen","Annotation Tools":"Text Tools",Patterns:"Muster","Prediction and Measurement Tools":"Prognose-/ Messwerkzeuge","Hide Drawings Toolbar":"Zeichnen-Werkzeugleiste ausblenden","Show Drawings Toolbar":"Zeichen-Werkzeugleiste anzeigen","Show Favorite Drawing Tools Toolbar":"Toolbar für favorisierte Zeichenwerkzeuge anzeigen",Icon:"Symbol",Icons:"Symbole","Remove Drawings & Indicators":"Zeichnungen und Indikatoren entfernen","Remove Drawings":"Zeichnungen entfernen","Remove Indicators":"Indikatoren entfernen","Compare or Add Symbol":"Symbol hinzufügen oder vergleichen",Favorites:"Favoriten","Fullscreen mode":"Vollbildmodus","Favorite Indicators":"Favorisierte Indikatoren","You have no Favorites Indicators yet":"Sie haben noch keine Favorisierten Indikatoren","Open Interval Dialog":"Intervall-Dialog öffnen","Number or {0}":"Zahl oder {0}","Add custom interval":"Individuelles Interval hinzufügen",Seconds_interval_group_name:"Sekunden",Minutes_interval_group_name:"Minuten",Hours_interval_group_name:"Stunden",Days_interval_group_name:"Tage",Weeks_interval_group_name:"Wochen",Months_interval_group_name:"Monate",Ranges_interval_group_name:"Bereiche","New drawings are replicated to all charts in the layout and shown when the same ticker is selected":"Neue Zeichnungen werden in alle Charts im Layout repliziert und angezeigt wenn der gleiche Ticker ausgewählt ist.","Open chart in popup":"Chart in Pop-up öffnen","Chart Properties":"Chart Einstellungen","Make a Copy":"Kopie erstellen","New Chart Layout":"Neues Chart Layout","Load Chart Layout...":"Chart-Layout laden...","Rename Chart Layout":"Chart-Layout umbenennen","Make a Copy...":"Kopie erstellen...","Manage Chart Layouts":"Chart Layouts verwalten","Save Indicator template...":"Indikator Vorlage wird gespeichert...","Save Indicator Template":"Indikator Vorlage wird gespeichert","Selecting this option will set the {symbol} symbol on the chart when this template is applied":"Wenn Sie diese Option wählen, wird das {symbol}-Symbol auf dem Chart gesetzt, wenn diese Vorlage angewendet wird.","Selecting this option will set the {interval} interval on the chart when this template is applied":"Durch Auswahl dieser Option wird das {interval}-Intervall im Chart festgelegt, wenn diese Vorlage angewendet wird","Study Template '{templateName}' already exists. Do you really want to replace it?":"Studienvorlage '{templateName}' existiert bereits. Möchten Sie diese wirklich ersetzen?",Templates:"Vorlagen","Indicator Templates":"Indikator Vorlagen","My templates":"Meine Vorlagen","Default templates":"Standardvorlagen","Undo {0}":"Rückgängig {0}","Redo {0}":"Wiederherstellen {0}","View Only Mode":"Ansichtsmodus","Add Alert":"Alarm hinzufügen","Add Alert on {series}":"Alarm hinzufügen {series}","Add Alert on {drawing}":"Alarm zu {drawing} hinzufügen","Edit {0} Alert...":"Nachricht {0} bearbeiten...","Extend Alert Lines":"Alarmlinien erweitern","Show alert label lines":"Alarm-Linien mit Label anzeigen","Hide alert label lines":"Alarm-Linien mit Label verbergen","Create Limit Order...":"Limit-Order erstellen...","Apply Manual Risk/Reward":"Manuelles Chancen-Risiko verwenden","Apply Manual Decision Point":"Manuellen Entscheidungspunkt verwenden","Analyze Trade Setup":"Trade Setup analysieren","Apply Elliott Wave":"Elliot Welle anwenden","Apply Elliott Wave Intermediate":"Intermediate Elliot Wellen anwenden","Apply Elliott Wave Major":"Haupt- Elliot Wellen anwenden","Apply Elliott Wave Minor":"Minimale Elliot Wellen anwenden","Apply WPT Up Wave":"WPT Up Wave anwenden","Up Wave 1 or A":"Aufwärtswelle 1 oder A","Up Wave 2 or B":"Aufwärtswelle 2 oder B","Up Wave C":"Aufwärtswelle C","Up Wave 3":"Aufwärtswelle 3","Up Wave 4":"Aufwärtswelle 4","Up Wave 5":"Aufwärtswelle 5","Apply WPT Down Wave":"WPT Down Wave anwenden","Down Wave 1 or A":"Abwärtsbewegung 1 oder A","Down Wave 2 or B":"Abwärtsbewegung 2 oder B","Down Wave C":"Abwärtsbewegung C","Down Wave 3":"Abwärtsbewegung 3","Down Wave 4":"Abwärtsbewegung 4","Down Wave 5":"Abwärtsbewegung 5","Pinned To Scale {label} (Hidden)":"An Skala angeheftet {label} (verborgen)","Pinned To Right Scale (Hidden)":"An rechte Skala angeheftet (verborgen)","Pin To Scale {label} (Hidden)":"An Skala anheften {label} (verborgen)","Pin To Right Scale (Hidden)":"An rechte Skala anheften (verborgen)","Pinned To Scale {label}":"An Skala angeheftet {label}","Pinned To Right Scale":"An rechte Skala angeheftet","Pin To Scale {label}":"An Skala angeheftet {label}","Pin To Right Scale":"An rechte Skala anheften","Pinned To Left Scale (Hidden)":"An linke Skala angeheftet (verborgen)","Pin To Left Scale (Hidden)":"An linke Skala anheften (verborgen)","Pinned To Left Scale":"An linke Skala angeheftet","Pin To Left Scale":"An linke Skala anheften","Pin To New Left Scale":"An neue linke Skala anheften","Pin To New Right Scale":"An neue rechte Skala anheften","Pin To Scale (Now {label})":"An Skala anheften (Jetzt {label})","Pin To Scale (Now No Scale)":"An Skala anheften (Jetzt keine Skala)","Pin To Scale (Now Left)":"An Skala anheften (Jetzt links)","Pin To Scale (Now Right)":"An Skala anheften (Jetzt rechts)","No Scale (Fullscreen)":"Keine Skalierung (Fullscreen)","Move To":"Bewegen nach","Existing Pane Above":"Vorhandener Bereich darüber","New Pane Above":"Neuer Bereich darüber","Existing Pane Below":"Vorhandener Bereich darunter","New Pane Below":"Neuer Bereich darunter","Bring to Front":"Ganz nach vorne bringen","Send to Back":"Ganz nach hinten verschieben","Bring Forward":"Nach vorne bringen","Send Backward":"Eins nach hinten verschieben","Visual Order":"Visuelle Reihenfolge",Lines:"Linien","Add Indicator/Strategy on {studyTitle}...":"Indikator / Strategie zu {studyTitle} hinzufügen...","Add Financial metric for {instrumentName}...":"Finanz-Metrik hinzufügen für {instrumentName}...","Settings...":"Einstellungen...","Apply Default":"Standard anwenden","Save As...":"Speichern unter...",Clone:"Duplizieren",Template:"Vorlage","Sync To All Charts":"Auf alle Charts anwenden",Unlock:"Entsperren",Lock:"Fixieren","Show Earnings":"Earnings zeigen","Show Dividends":"Dividenden anzeigen","Show Splits":"Splits anzeigen","Show All Ideas":"Alle Ideen anzeigen","Show Ideas of Followed Users":"Ideen von Usern denen ich folge anzeigen","Show My Ideas Only":"Nur meine Ideen anzeigen","Hide Events on Chart":"Ereignisse im Chart ausblenden","Add this Symbol to Entire Layout":"Das Symbol dem gesamten Layout hinzufügen","Add this Indicator to Entire Layout":"Den Indikator dem gesamten Layout hinzufügen","Add this Strategy to Entire Layout":"Die Strategie dem gesamten Layout hinzufügen","Add this Financial Metric to Entire Layout":"Diese Finanzkennzahl zum gesamten Layout hinzufügen","Symbol Info...":"Symbol-Info...","Add Symbol_compare_or_add_symbol_dialog":"Symbol hinzufügen","Overlay the main chart":"Hauptchart überlagern",Absolute:"Absolut","On The Left":"Auf der Linken Seite","On The Right":"Auf der Rechten Seite","Move Scale To Left":"Skala nach links bewegen","Move Scale To Right":"Skala nach rechts bewegen","Change Resolution":"Auflösung ändern","Add to Watchlist {0}":"{0} zur Watchlist hinzufügen",Eraser:"Radierer","Trend Line":"Trendlinie","Horizontal Line":"Horizontale Linie","Vertical Line":"Vertikale Linie",Arrow:"Pfeil",Ray:"Strahl",Extended:"Verlängert","Parallel Channel":"Paralleler Kanal","Schiff Pitchfork":"Schiff-Pitchfork","Gann Square":"Gann Square (Quadrat)","Gann Fan":"Gann Fächer","Trend-Based Fib Extension":"Trendbasierte Fib-Extension","Fib Speed Resistance Fan":"Fib Speed Resistance Fan (Fächer)","Fib Time Zone":"Fib Zeitzonen","Circle Lines":"Kreislinien","Fib Circles":"Fib Kreise","Fib Speed Resistance Arcs":"Fib Speed Resistance Arcs (Bögen)",Rectangle:"Rechteck",Triangle:"Dreieck",Polyline:"Linienzug",Path:"Pfad",Arc:"Bogen",Text_tool:"Text","Anchored Text":"Verankerter Text",Balloon:"Sprechblase","Price Label":"Preis-Label","Elliott Wave Subminuette":"Subminuette Elliott Welle","Elliott Wave Minor":"Minor Elliott Welle","Elliott Wave Circle":"Elliot Wellenkreis","Elliott Minor Retracement":"Minor Elliott Retracement","Elliott Major Retracement":"Major Elliott Retracement",Brush:"Pinsel",Forecast:"Prognose","Reset Chart":"Chart zurücksetzen","Invert Scale":"Skala invertieren","Auto (Fits Data To Screen)":"Auto (an Bildschirm anpassen)","Lock Price To Bar Ratio":"Kurs zu Balken Verhältnis fixieren","Indexed to 100":"Auf 100 indexiert",Logarithmic:"Logarithmisch",Undo:"Rückgängig",Redo:"Wiederherstellen","Time Zone":"Zeitzone","Change Symbol...":"Symbol ändern...","Change Interval...":"Intervall ändern","Add To Watchlist":"Zur Watchlist Hinzufügen","Add To Text Notes":"Füge zu Textnotizen hinzu","Reset Time Scale":"Zeitachse zurücksetzen","Insert Drawing Tool":"Zeichenwerkzeug einfügen","All Indicators And Drawing Tools":"Alle Indikatoren und Zeichen-Tools","Apply these Indicators to Entire Layout":"Diese Indikatoren dem gesamten Layout hinzufügen","Insert Indicator...":"Indikator einfügen...","Compare or Add Symbol...":"Symbol hinzufügen oder vergleichen...","Compare...":"Vergleichen...","Object Tree...":"Objekt Baum...","Lock/Unlock":"Fixieren / Lösen","Scale Price Chart Only":"Nur den Preis-Chart vergrößern","Drawings Toolbar":"Zeichen-Werkzeugleiste","Stay in Drawing Mode":"Im Zeichenmodus bleiben","Stay In Drawing Mode":"Im Zeichenmodus bleiben","Sync drawings to all charts":"Zeichen-Tools mit allen Charts synchronisieren","Lock All Drawing Tools":"Alle Zeichen-Tools fixieren","Lock drawings":"Zeichnungen fixieren","Hide All Drawing Tools":"Alle Zeichen-Tools verbergen","Hide Marks On Bars":"","Extended Trading Hours":"Erweiterte Tradingzeit","Change Extended Hours":"Erweiterte Stunden ändern","Symbol Last Price Label":"Letzter Wert des Symbols Label","Show Symbol Last Value":"Letzten Symbol-Wert anzeigen","Previous Day Close Price Line":"Vortages-Schlusskurs Linie","Symbol Name Label":"Symbol Namenslabel","Indicator Last Value Label":"Label für letzten Indikatorwert","Show Indicator Last Value":"Letzten Indikatorwert anzeigen","Indicator Name Label":"Label für Indikatornamen","Bid and Ask Labels":"Bid und Ask Labels","Pre/Post Market Price Label":"Pre/Post Market Kurs Label","Pre/Post Market Price Line":"Pre/Post Market Kurs Linie","Financials Last Value Label":"Finanzkennzahlen Letzter Wert Label","Financials Name Label":"Finanzkennzahlen Name Label","Countdown To Bar Close":"Countdown zum nächsten Balken","Go to Date...":"Gehe zu Datum...","Source Code...":"Quellcode...","Draw Horizontal Line Here":"Hier eine horizontale Linie Zeichnen","Draw Vertical Line Here":"Hier eine vertikale Linie Zeichnen","Draw Cross Line Here":"Hier ein Fadenkreuz zeichnen","Draw Trend Line":"Trendlinie zeichnen","Draw Fib Retracement":"Fib-Retracement zeichnen","Session Breaks":"Sitzungspausen",Warning:"Warnung","Company Comparison":"Unternehmen vergleichen","Zoom Out":"Verkleinern","Zoom In":"Vergrößern","Maximize chart":"Chart maximieren","Restore chart":"Chart wiederherstellen","Scroll to the Left":"Nach links scrollen","Scroll to the Right":"Nach rechts scrollen","Scroll to the Most Recent Bar":"Zum aktuellsten Balken scrollen",Mixed:"Gemischt","Reset Settings":"Einstellungen zurücksetzen","Save As Default":"Als Standard speichern",Defaults:"Standardeinstellungen",Help:"Hilfe","Hide Favorite Drawing Tools Toolbar":"Toolbar für favorisierte Zeichenwerkzeuge verbergen","Show Symbol":"Symbol anzeigen","Show Open market status":"Markt-Öffnungs-Status anzeigen","Show OHLC Values":"OHLC-Werte anzeigen","Show Bar Change Values":"Balken Veränderungswerte anzeigen","Show Indicator Titles":"Indikatoren Titel anzeigen","Show Indicator Arguments":"Indikator-Argumente anzeigen","Show Indicator Values":"Indikator-Werte anzeigen","Wrap text":"Textumbruch","Hide Indicator Legend":"Indikator-Legende verbergen","Show Indicator Legend":"Indikator-Legende anzeigen","Show Object Tree":"Objektbaum anzeigen","Could not get Pine source code.":"Pine Quellcode konnte nicht geladen werden","Flag Symbol":"Symbol markieren","Unflag Symbol":"Markierung aufheben","{0} — drawing a straight line at angles of 45":"{0} — Zeichnen einer geraden Linie im Winkel von 45°","{0} — circle":"{0} — Kreis","{0} — square":"{0} — Quadrat","XABCD Pattern":"XABCD-Muster","ABCD Pattern":"ABCD Muster","Arrow Mark Down":"Pfeil nach unten","Arrow Mark Left":"Pfeil nach links","Arrow Mark Right":"Pfeil nach rechts","Arrow Mark Up":"Pfeil nach oben","Bars Pattern":"Balkenmuster","Double Curve":"Doppelkurve",Curve:"Kurve","Cyclic Lines":"Zyklische Linien","Date and Price Range":"Daten- und Preisbereich","Disjoint Channel":"Entkoppelter Kanal","Elliott Correction Wave (ABC)":"Elliott Korrektur Welle (ABC)","Elliott Double Combo Wave (WXY)":"Elliot Doppel Combo Welle (WXY)","Elliott Impulse Wave (12345)":"Elliot Impuls Welle (12345)","Elliott Triangle Wave (ABCDE)":"Elliot Dreiecks-Welle (ABCDE)","Elliott Triple Combo Wave (WXYXZ)":"Elliot Dreifach-Combo-Welle (WXYXZ)","Fib Channel":"Fib Kanal","Fib Spiral":"Fib Spirale","Fib Wedge":"Fib Keil","Flag Mark":"Flagge","Anchored VWAP":"Verankerter VWAP","Gann Square Fixed":"Gann-Square fixiert","Gann Box":"Gann-Box","{0} — fixed increments":"{0} — feste Abstufungen","Head and Shoulders":"Kopf und Schultern","Horizontal Ray":"Unterstützung-/Widerstandslinie","Font Icons":"Schriftartsymbole",Note:"Anmerkung","Anchored Note":"Verankerte Anmerkung","Arrow Marker":"Pfeil-Markierung","Price Range":"Preisspanne",Projection:"Projektion","Regression Trend":"Regressionstrend","Long Position":"Long-Position","Short Position":"Short-Position","Rotated Rectangle":"Drehbares Rechteck","Modified Schiff Pitchfork":"Modifizierte Schiff-Pitchfork","Sine Line":"Sinuslinie","Three Drives Pattern":"Three-Drives-Muster","Time Cycles":"Zeitzyklen","Trend Angle":"Trendwinkel","Trend-Based Fib Time":"Trendbasierte Fib-Zeit","Info Line":"Info Linie","Triangle Pattern":"Dreiecksmuster","Cross Line":"Fadenkreuz",Cross:"Fadenkreuz",Dot:"Punkt","Show Hidden Tools":"Versteckte Tools anzeigen","Magnet Mode snaps drawings placed near price bars to the closest OHLC value":"Der Magnet-Modus zieht Zeichnungen, die in der Nähe von Preisbalken platziert sind, an den nächstgelegenen OHLC-Wert.",Measure:"Messen","{0} + Click on the chart":"{0} + Klick auf das Chart","Bar #":"Balken Nr.","Set line tool width":"Linien-Werkzeug Breite festlegen","Set line tool widths":"Linien-Werkzeug Breiten festlegen","Line tool width":"Linien-Werkzeug Breite","Line tool widths":"Linien-Werkzeug Breiten","Stop syncing":"Synchronisierung stoppen","Sync to all charts":"Alle Charts synchronisieren",Color:"Farbe","Background Color":"Hintergrundfarbe","Left End":"linkes Ende","Right End":"Rechtes Ende","Text color":"Textfarbe","Profit Background Color":"Gewinn Farbe Hintergrund","Stop Background Color":"Hintergrundfarbe stoppen","Border color":"Rahmenfarbe","Border Color":"Rahmenfarbe","Font Size":"Schriftgröße","Marker Color":"Stiftfarbe","Arrow color":"Pfeilfarbe","Line Width":"Linienbreite","One color for all lines":"Eine Farbe für alle Linien","Flag color":"Flaggen Farbe","Background color 1":"Hintergrundfarbe 1","Background color 2":"Hintergrundfarbe 2","Apply Defaults":"Voreinstellungen anwenden","Template name":"Name der Vorlage","Change Seconds From":"Wechsle Sekunden von","Change Seconds To":"Wechsle Sekunden zu",Minutes:"Minuten","Change Minutes From":"Wechsle Minuten von","Change Minutes To":"Wechsle Minuten zu",Hours:"Stunden","Change Hours To":"Stunden ändern in",Days:"Tage","Change Days To":"Tage ändern in",Weeks:"Wochen",Months:"Monate",Ranges:"Bereiche",Actual:"Aktuell",Previous:"Vorherig","Double click":"Doppelklick","Delete pane":"Bereich Löschen","Move pane up":"Bereich nach oben bewegen","Move pane down":"Bereich nach unten bewegen","Maximize pane":"Bereich Maximieren","Restore pane":"Bereich wiederherstellen","Manage panes":"Bereiche Verwalten","Paste %s":"Einfügen %s","Time Scale":"Zeitachse","Lock Vertical Line On Time Axis":"Vertikale Linie auf Zeitachse fixieren","Create Vertical Line":"Vertikale Linie erstellen","Create Horizontal Line":"Horizontale Linie erstellen","Lock Scale":"Skalierung fixieren","Merge All Scales Into One":"Alle Skalen zu einer einzigen zusammenfügen","Reset Price Scale":"Kursskala zurücksetzen",Percent_scale_menu:"Prozent","Indexed to 100_scale_menu":"Auf 100 indexiert",Logarithmic_scale_menu:"Logarithmisch",Regular_scale_menu:"Regulär","No Overlapping Labels_scale_menu":"Keine überlappenden Label","Invert Scale_scale_menu":"Skala invertieren",Labels:"Beschriftungen","Status line":"Status Zeile",Appearance:"Erscheinung","Vert Grid Lines":"Vertikale Gitterlinien","Horz Grid Lines":"Horizontale Gitterlinien","Scales text":"Skalen Text","Scales lines":"Skalen Zeilen",Watermark:"Wasserzeichen","Top Margin":"Oberer Rand","Navigation Buttons":"Navigationstasten","Pane Buttons":"Bereichs-Buttons","Bottom Margin":"unterer Abstand","Right Margin":"Rechter Seitenrand",bars_unit:"Balken","OHLC Values":"OHLC Werte","Bar Change Values":"Balken Änderungswerte","Indicator Titles":"Titel des Indikators","Indicator Arguments":"Funktionsargument des Indikators","Indicator Values":"Werte des Indikators","Show Price":"Preis anzeigen","Show Time":"Zeit Anzeigen","Symbol Previous Day Close Price Label (Intraday Only)":"Label für vorherigen Schlusskurs des Symbols (nur intraday)","Scales Placement":"Skalen Platzierung","Date Format":"Datumsanzeige","Lock Price to Bar Ratio":"Kurs zu Balken Verhältnis fixieren","No Overlapping Labels":"Keine überlappenden Label","Price and Percentage Value":"Preis und Prozentwert","Value according to Scale":"Wert laut Skala","Show Buy/Sell Buttons":"Buy/Sell Buttons anzeigen","Box Size":"Boxgröße","Color Bars Based on Previous Close":"Balken aufgrund von vorherigem Schlußkurs färben.","HLC Bars":"HLC-Balken","Thin Bars":"Dünne Balken",Body:"Körper","Price Source":"Preisquelle","Top Line":"Oberste Linie","Bottom Line":"Unterste Linie","Fill Top Area":"Füllung oberer Bereich","Fill Bottom Area":"Füllung unterer Bereich","Base Level":"Grundwert","Extend Lines Left":"Linien nach links erweitern",Left:"Links",Center:"Zentrieren",Right:"Rechts","Show Middle Point":"Zeige Mittelpunkt","Show Price Range":"Preisspanne anzeigen","Show Bars Range":"Balkenrange anzeigen","Show Date/Time Range":"Datums-/Zeitspanne anzeigen","Show Distance":"Abstand anzeigen","Show Angle":"Winkel anzeigen","Always Show Stats":"Statistiken immer anzeigen","Extend left line":"Linie nach links verlängern","Extend right line":"Linie nach rechts verlängern","Text Wrap":"Textumfluss",Label:"Beschriftung","Show text":"Text anzeigen",Mode:"Modus",Mirrored:"Gespiegelt",Flipped:"Umgedreht","HL Bars":"High-Low Bars","Line - Close":"Linie - Schluss","Line - Open":"Linie - Eröffnung","Line - High":"Linie - Hoch","Line - Low":"Linie - Tief","Line - HL/2":"Linie - HT/2",Wave:"Welle",Degree:"Grad","Use one color":"Eine Farbe verwenden",Levels:"Level","Coeffs As Percents":"Koeffizienten als Prozentangaben","Levels Line":"","Extend Right":"Nach rechts verlängern","Extend Left":"Nach links verlängern","Extend Lines Right":"Linien nach rechts erweitern","Fib levels based on log scale":"Fib Level, basierend auf logarithmischer Skalierung",Values:"Werte",Percents:"Prozente",Top:"Oben",Middle:"Mitte",Bottom:"Unten","Full Circles":"Volle Kreise","Price Levels":"Preisniveaus","Time Levels":"Zeitebenen","Left Labels":"Beschriftungen links","Right Labels":"Beschriftung Rechts","Top Labels":"Markierungen an Oberseite","Bottom Labels":"Beschriftungen",Counterclockwise:"Gegen den Uhrzeigersinn",Flag:"Flagge","Ranges and ratio":"Bereiche und Verhältnis",Fans:"Fächer",Arcs:"Bögen",Angles:"Winkel","Extend top":"Nach oben verlängern","Extend bottom":"Nach unten verlängern","Extend left":"Nach links verlängern","Extend right":"Nach rechts verlängern","Label background":"Beschriftung-Hintergrund",Transparency:"Transparenz","Avg HL in minticks":"Durchschnittliche HL in Minticks",Variance:"Abweichung","#1 (price)_linetool point":"#1 (Preis)","#1 (price, bar)_linetool point":"#1 (Preis, Bar)","#{0} (price, bar)_linetool point":"#{0} (Preis, Bar)",Channel:"Kanal",Median:"Medianer Wert","Extend Lines":"Linien verlängern","Modified Schiff":"Schiff modifiziert","Source text":"Quell Text","Source background":"Quelle Hintergrund","Source border":"Quelle Grenze","Target text":"Ziel Text","Target background":"Ziel Hintergrund","Target border":"Ziel Grenze","Success text":"Erfolg Text","Success background":"Erfolg Hintergrund","Failure text":"Fehlschlag Text","Failure background":"Fehlschlag Hintergrund","Stop color":"Stop Farbe","Target color":"Kursziel Farbe:","Compact stats mode":"Kompakter Statistik-Modus","Entry price":"Einstiegspreis","Profit level":"Profit Level","Stop level":"Stop Level","Account size":"Kontogröße","Always show stats":"Statistik immer anzeigen","Show price labels":"Preislabel anzeigen",Angle:"Winkel","#1 (bar)_linetool point":"#1 (Bar)","Adjust Data for Dividends":"Daten für Dividenden anpassen","Extended Hours (Intraday Only)":"Verlängerte Handelszeit (nur Intraday)","Last Price Line":"Letzter Kurs Linie","Bid and Ask lines":"Bid und Ask Linien",Precision:"Präzision",High:"Hoch",Low:"Tief","(H + L)/2":"","(H + L + C)/3":"(H + L + C)/3\n(H + L + C)/3","(O + H + L + C)/4":"",Simple:"Einfach","With Markers":"Mit Markierungen",Step:"Stufe",Default:"Standard","Override Min Tick":"Min Tick überschreiben",Vertical:"Vertikal","Text alignment":"Textausrichtung","Text orientation":"Text Ausrichtung","Line With Breaks":"Linie mit Unterbrechungen","Step Line":"Stufenlinie",Histogram:"Histogramm",Cross_chart_type:"Cross","Area With Breaks":"Fläche mit Lücken",Columns:"Säulen",Circles:"Kreise","Above Bar":"Oberhalb des Balkens","Below Bar":"Unterhalb der Bars","Initial capital":"Anfangskapital","Base currency":"Basis-Währung","Order size":"Umfang der Order","orders_Pyramiding: count orders":"Orders",Commission:"Kommission","Verify Price for Limit Orders":"Kurs für Limit-Orders bestätigen","ticks_slippage ... ticks":"Ticks",Recalculate:"Neu berechnen","After Order is Filled":"Nachdem die Order gefüllt wurde","On Every Tick":"Auf jeden Tick","{currency} per order":"{currency} pro Währung","{currency} per contract":"{currency} pro Kontrakt","% of equity":"% Aktien",high:"High",low:"Low",close:"Close",Offset:"Ausgleich","Main chart symbol_input":"Symbol im Hauptchart","Another symbol_input":"Weiteres Symbol","Same as symbol":"Gleich dem Symbol","Change Thickness":"Stärke ändern","Change Color":"Farbe ändern","Change Opacity":"Transparenz ändern","Change Font":"Schriftart ändern","Change Font Size":"Schriftgröße ändern","Change Line Style":"Linienart ändern","Change Min Tick":"Min Tick ändern","Change Precision":"Präzision ändern","Change Visibility":"Sichtbarkeit ändern","Change Value":"Wert ändern","Change Char":"Zeichen ändern","Change Location":"Ort ändern","Width (% of the Box)":"Breite (% der Box)","Show Values":"Werte anzeigen","Text Color":"Textfarbe","Trades on Chart":"Trades auf dem Chart","Change Plot Type":"Darstellungsart ändern","Change Price Line":"Kurslinie ändern","Labels Font":"Label Schrift","Show Labels":"Beschriftungen anzeigen","Change Shape":"Form ändern","Symbol Type":"Symboltyp","Chart layout name":"Chart Layout Name","Enter a new chart layout name":"Chart Layout neu benennen","Please enter chart layout name":"Bitte geben Sie einen Namen für das Chart-Layout ein.","Save New Chart Layout":"Neues Chart-Layout speichern","Copy Chart Layout":"Chart Layout kopieren","{0} copy_ex: AAPL chart copy":"{0} Kopieren","Data problem":"Daten Problem","Data is delayed":"Daten sind verzögert","End of day data":"End of Day Daten","One update per second":"Ein Update pro Sekunde","{symbolName} data is delayed by {time} minutes.":"{symbolName} Daten sind um {time} Minuten verzögert.","{listedExchange} real-time data is available for free to registered users.":"{listedExchange} Echtzeit Daten sind für registrierte User kostenlos verfügbar.","To get real-time data for {description}, please buy the real-time data package.":"Um Echtzeit Daten für {description} zu erhalten, kaufen Sie bitte das Echtzeit Datenpaket.","Real-time data for {description} is not supported right now. We may support it in the future.":"Echtzeitdaten für {description} werden derzeit nicht unterstützt. Wir werden diese evtl. künftig unterstützen.","Data is updated once a day.":"Daten werden einmal täglich aktualisiert.","Data on our Basic plan is updated once per second, even if there are more updates on the market.":"Daten für Free-User werden nur ein mal pro Sekunde aktualisiert, auch wenn mehr Änderungen im Markt geschehen.","Data is updated once per second, even if there are more updates on the market.":"Daten werden nur einmal pro Sekunde aktualisiert, auch wenn es mehr Änderungen am Markt gibt.","Paid plans feature faster data updates.":"Kostenpflichtige Abos enthalten schnellere Daten-Updates","Real-time data for {symbolName} is provided by {exchange} exchange.":"Echtzeit Daten für {symbolName} werden von der {exchange} Börse bereitgestellt.","This data is real-time, but it’s slightly different to its official counterpart coming from primary exchanges.":"Diese Daten sind in Echtzeit, können sich jedoch leicht von deren offiziellen Handelsplätzen, primären Börsen, unterscheiden.","Create a free account":"Erstellen Sie einen kostenlosen Account","This symbol doesn't exist, please pick another one.":"Dieses Symbol existiert nicht. Bitte wählen Sie ein anderes Symbol.","Market open":"Markt ist geöffnet","Pre-market":"Vorbörslich","Post-market":"Nachbörslich","Market closed":"Markt ist geschlossen","All's well — Market is open.":"Alles ist gut — Markt ist geöffnet.","Morning. Market is open for pre-market trading.":"Guten Morgen. Markt befindet sich im vorbörslichen Handel.","Evening. Market is open for post-market trading.":"Guten Abend. Markt befindet sich im nachbörslichen Handel.","Time for a walk — this market is closed.":"Zeit für einen Spaziergang — Dieser Markt ist geschlossen.","Market is currently on holiday. Lucky them.":"Der Markt ist derzeit im Urlaub. Was für ein Leben.","Replay mode":"Wiedergabe-Modus","You're in Replay mode. You're in Replay mode. You're in Replay mode.":"Sie sind im Wiedergabe-Modus. Sie sind im Wiedergabe-Modus. Sie sind im Wiedergabe-Modus.","You can turn this data on or off.":"Sie können diese Daten Ein- und Ausschalten.","Point bar":"Point Bar","Change band background":"Bandhintergrund ändern","Change area background":"Bereichshintergrund ändern","Do you really want to delete Study Template '{0}' ?":"Möchten Sie die Studienvorlage '{0}' wirklich löschen?","No symbols matched your criteria":"Kein passendes Symbol für Ihr Kriterium gefunden","Quandl is a huge financial database that we have connected to TradingView. Most of its data is EOD and is not updated in real-time, however the information may be extremely useful for fundamental analysis.":"Quandl ist eine riesige Finanzdatenbank, welche wir an TradingView angeschlossen haben. Die meisten Daten sind EOD und werden nicht Echtzeit aktualisiert, dennoch könnte die Information für die Fundamentalanalyse sehr nützlich sein.","Read our blog for more info!":"Lesen Sie unseren Blog, um mehr zu erfahren!","Modify Order...":"Order modifizieren...","Cancel Order":"Auftrag abbrechen","Protect Position...":"Position absichern...","Close Position":"Position Schließen","Reverse Position":"Position Umkehren","Script name":"Skripname","Visible on Mouse Over":"Sichtbar, wenn der Mauszeiger darüber bewegt wird","Always Visible":"Immer sichtbar","Always Invisible":"Immer verborgen","Send {title} backward":"{title} nach hinten bewegen","Bring {title} forward":"{title} nach vorne bewegen","Insert {title} after {target}":"{title} nach {target} einfügen","Insert {title} before {target}":"{title} vor {target} einfügen","Create {name} line tool":"{name} Linien-Tool erstellen","Move scale":"Skala bewegen","Clone line tools":"Linien-Klon Werkzeug","Move {title} To New Price Scale":"{title} zu neuer Kursskala bewegen","Move {title} To New Left Scale":"{title} zu neuer Kursskala, links, bewegen","Make {title} No Scale (Full Screen)":"Skalierung für {title} aufheben (Vollbild)","Remove all studies":"Alle Studien entfernen","Remove all studies and drawing tools":"Alle Studien und Zeichentools entfernen","{0} bars":"{0} Balken",Mar:"Mrz",Oct:"Okt",Dec:"Dez","Fraction part is invalid.":"Dieser Teil ist ungültig","Second fraction part is invalid.":"Zweiter Bruchteil ungültig",d_dates:"t",h_dates:"h",m_dates:"m",s_dates:"s","Last available bar":"Letzter vorhandener Balken","Create line tools group from selection":"Linien-Tool-Gruppe aus Auswahl erstellen","Removing line tools group {name}":"Entferne {name} Linien-Tool-Gruppe","Add line tool {lineTool} to group {name}":"Linien Tool {lineTool} zu Gruppe {name} hinzufügen","Make group {group} visible":"Gruppe {group} sichtbar machen","Make group {group} invisible":"Gruppe {group} unsichtbar machen","Lock group {group}":"Gruppe {group} fixieren","Unlock group {group}":"Gruppe {group} Fixierung aufheben","Rename group {group} to {newName}":"Gruppe {group} zu {newName} umbenennen","Restore Size":"Größe Wiederherstellen",Minor_wave:"Minor",Minute_wave:"Minute","Extended Line":"Verlängerte Linie","Left Shoulder":"Linke Schulter","Right Shoulder":"Rechte Schulter",Head:"Kopf",SUCCESS:"ERFOLG",FAILURE:"FEHLER",in_dates:"in","{0} P&L: {1}":"{0} G&V: {1}",Open_line_tool_position:"Offener",Closed_line_tool_position:"Geschlossen","Risk/Reward Ratio: {0}":"Chance/Risiko Verhältnis: {0}","Stop: {0} ({1}) {2}, Amount: {3}":"Stop: {0} ({1}) {2}, Betrag: {3}","Target: {0} ({1}) {2}, Amount: {3}":"Ziel: {0} ({1}) {2}, Betrag: {3}","Qty: {0}":"Anz: {0}","Risk/Reward short":"Chance/Risiko Short","distance: {0}":"Abstand: {0}",s_interval_short:"s",R_interval_short:"R",day:"Tag",day_plural:"Tage",week:"Woche",week_plural:"Wochen",second:"Sekunde",second_plural:"Sekunden",minute:"Minute",minute_plural:"Minuten",hour:"Stunde",hour_plural:"Stunden",range:"Bereich",range_plural:"Bereiche","Stack On The Left":"Links sammeln","Stack On The Right":"Rechts sammeln","not authorized":"nicht autorisiert",ext_shorthand_for_extended_session:"ext",O_in_legend:"O",H_in_legend:"H",L_in_legend:"L",C_in_legend:"C",HL2_in_legend:"HL2",HLC3_in_legend:"HLC3",OHLC4_in_legend:"OHLC4","compiling...":"wird erstellt...","loading...":"lade...","Arrow Down":"Pfeil Abwärts","Arrow Up":"Pfeil Aufwärts",Circle:"Kreis","Label Down":"Label Abwärts","Label Up":"Label Aufwärts",Square:"Rechteck","Triangle Down":"Dreieck Abwärts","Triangle Up":"Dreieck Aufwärts","Add line tool(s) {lineTool} to group {group}":"Linien Tool(s) {lineTool} zu Gruppe {group} hinzufügen","Apply study template {template}":"Studienvorlage anwenden {template}","Create line tools group":"Linien-Tool-Gruppe erstellen","Exclude line tools from group {group}":"Linien-Tools aus Gruppe {group} ausschliessen","Move All Scales To Left":"Alle Skalen nach links bewegen","Move All Scales To Right":"Alle Skalen nach rechts bewegen",Notification:"Benachrichtigung","charts by TradingView":"Charts von TradingView","powered by TradingView":"unterstützt von TradingView","by TradingView":"von TradingView","Custom color...":"Benutzerdefinierte Farbe...",Content:"Inhalt","Close message":"Nachricht schließen","Type the interval number for minute charts (i.e. 5 if it is going to be a five minute chart). Or number plus letter for H (Hourly), D (Daily), W (Weekly), M (Monthly) intervals (i.e. D or 2H)":"Geben Sie die Intervall-Nummer für Minuten-Charts ein (z.B. 5, wenn es sich um einen Fünf-Minuten-Chart handelt). Oder Zahl plus Buchstabe für H (stündlich), D (täglich), W (wöchentlich), M (monatlich) Intervalle (d.h. D oder 2H)","Type the interval number for minute charts (i.e. 5 if it's going to be a five minute chart). Or number plus letter for other intervals: S for 1 second chart (15S for 15 second chart, etc.), H (Hourly), D (Daily), W (Weekly), M (Monthly) intervals (i.e. D or 2H)":"Geben Sie die Intervallnummer für Minuten-Charts ein (d.h. 5, wenn es sich um ein Fünf-Minuten-Chart handeln soll). Oder Nummer und Buchstabe für andere Intervalle: S für 1-Sekunden-Chart (15S für 15-Sekunden-Chart, etc.), H (stündliche), D (tägliche), W (wöchentliche), M (monatliche) Intervalle (z.B. D oder 2H)","Change Interval":"Intervall ändern","Not applicable":"Nicht anwendbar",Confirmation:"Bestätigung","Do you really want to delete Chart Layout '{0}' ?":"Möchten Sie das Chartlayout '{0}' wirklich löschen?","Load layout":"Layout wird geladen","Layout name":"Layout Name","Sort by layout name, date changed":"Nach Layout-Name sortieren, Änderungsdatum","Layout name (A to Z)":"Layout Name (A bis Z)","Layout name (Z to A)":"Layout Name (Z-A)","Date modified (oldest first)":"Bearbeitungs-Datum (ältestes zuerst)","Date modified (newest first)":"Bearbeitungs-Datum (neuestes zuerst)","You are notified":"Sie werden benachrichtigt","Saved indicators":"Gespeicherte Indikatoren","Remember Symbol":"Symbol merken","Remember Interval":"Interval merken","Confirm Remove Study Tree":"Bestätigen Sie das Löschen des gesamten Studienbaums","Do you really want to delete study and all of it's children?":"Möchten Sie wirklich diese Studie und ihre Ableger löschen?","Studies limit exceeded: {0} studies per layout.\nPlease, remove some studies.":"Studien Limit erreicht: {0} Studien pro Layout.\nBitte entfernen Sie einige Studien.","No indicators matched your criteria.":"Keine passenden Indikatoren zu Ihren Kriterien gefunden",Closed:"Geschlossen","Symbol Name":"Symbolname","Symbol Description":"Symbolbeschreibung","Current Contract":"Aktueller Kontrakt","Point Value":"Punktwert","Listed Exchange":"Gelistete Börse","Pip Size":"Pip-Größe","Tick Size":"Tick Größe",Session:"Sitzung","URL cannot be received":"URL kann nicht empfangen werden","Error while trying to create snapshot.":"Fehler während der Erstellung eines Snapshot",Sunday:"Sonntag",Monday:"Montag",Tuesday:"Dienstag",Wednesday:"Mittwoch",Thursday:"Donnerstag",Friday:"Freitag",Saturday:"Samstag",Sun:"Son",Tue:"Die",Wed:"Mi",Thu:"Do",Fri:"Fr",Sat:"Sa",Su_day_of_week:"Su",Mo_day_of_week:"Mo",Tu_day_of_week:"Tu",We_day_of_week:"We",Th_day_of_week:"Th",Fr_day_of_week:"Fr",Sa_day_of_week:"Sa","{specialSymbolOpen}Today at{specialSymbolClose} {dayTime}":"{specialSymbolOpen}Heute um{specialSymbolClose} {dayTime}","{specialSymbolOpen}Tomorrow at{specialSymbolClose} {dayTime}":"{specialSymbolOpen}Morgen um{specialSymbolClose} {dayTime}","{dayName} {specialSymbolOpen}at{specialSymbolClose} {dayTime}":"{dayName} {specialSymbolOpen}um{specialSymbolClose} {dayTime}","{specialSymbolOpen}Yesterday at{specialSymbolClose} {dayTime}":"{specialSymbolOpen}Gestern um{specialSymbolClose} {dayTime}","{specialSymbolOpen}Last{specialSymbolClose} {dayName} {specialSymbolOpen}at{specialSymbolClose} {dayTime}":"{specialSymbolOpen}Zuletzt{specialSymbolClose} {dayName} {specialSymbolOpen}um{specialSymbolClose} {dayTime}","just now":"gerade","in %s_time_range":"in %s","%s ago_time_range":"vor %s","%d minute":"%d Minute","%d minute_plural":"%d Minuten","an hour":"eine Stunde","%d hour":"%d Stunde","%d hour_plural":"%d Stunden","a day":"ein Tag","%d day":"%d Tag","%d day_plural":"%d Tage","a month":"ein Monat","%d month":"%d Monat","%d month_plural":"%d Monate","a year":"ein Jahr","%d year":"%d Jahr","%d year_plural":"%d Jahre","The publication preview could not be loaded. Please disable your browser extensions and try again.":"Die Veröffentlichungsvorschau konnte nicht geladen werden. Bitte deaktivieren Sie Ihre Browser-Erweiterungen und versuchen Sie es erneut.","Script may be not updated if you leave the page.":"Das Skript wird möglicherweise nicht aktualisiert, wenn Sie die Seite verlassen.","This Chart Layout has a lot of objects and can't be published. Please remove the unused objects (drawings/indicators) through the Object Tree to continue publishing.":"Dieses Layout enthält zu viele Objekte und kann nicht veröffentlicht werden. Bitte entfernen Sie überflüssige Zeichnungen und / oder Texte von diesem Entwurf und versuchen Sie es erneut.","Error occurred while publishing":"Während der Veröffentlichung ist ein Fehler aufgetreten","Your chart is being saved, please wait a moment before you leave this page.":"Ihre Chart wurde gespeichert, bitte warten Sie einen Moment bevor Sie diese Seite verlassen.",futures:"Futures",index:"Index",stock:"Aktie",Light_colorThemeName:"Hell",Dark_colorThemeName:"Dunkel","Please enter theme name":"Bitte, geben Sie einen Namen für das Design ein.","Theme name":"Design-Name","Save Theme As":"Design speichern als","Color Theme '__themeName__' already exists. Do you really want to replace it?":'Das Farbschema "__themeName__" gibt es schon. Wollen Sie es wirklich ersetzen?',"Do you really want to delete Color Theme '{0}' ?":"Möchten Sie das Farbschema '{0}' wirklich löschen?","Dashed Line":"Gestrichelte Linie","Dotted Line":"Gepunktete Linie",Thickness:"Stärke","Add Symbol":"Symbol hinzufügen","Confirm Inputs":"Eingabe bestätigen","Chart settings":"Chart Einstellungen","Apply to all":"Auf alle Anwenden","No results found":"Keine Ergebnisse","Add Custom Color_Color Picker":"Individuelle Farbe hinzufügen","Opacity_Color Picker":"Deckkraft","Add_Color Picker":"Hinzufügen","Please enter the right date format yyyy-mm-dd":"Bitte geben Sie das korrekte Datumsformat ein yyyy-mm-dd","Please enter the right time format hh:mm":"Bitte geben Sie das korrekt Zeitformat ein hh:mm"}</script></body></html>