# A generic NodeJS chart
features dynamic volume mounts, which should hopefully never be used.

## Changelog

### 2.1.5
Remove unused code

### 2.1.4
Fix basic auth

### 2.1.2
Add basic auth (refer to USING_BASIC_AUTH.md)

### 2.1.1
Fix tolerations placement

### 2.1.0
Add tolerations and node selector

### 2.0.0
Add autoscaling

### 1.3.1
Fix the port used for the liveness probe

### 1.3.0
Add option for liveness probe

### 1.2.0
Add option for setting the container name

### 1.0.0
Initial release in favor of nodeapi

