import clsx from 'clsx'
import type React from 'react'

/**
 * InputProps interface
 * This interface is used to define the props for the Input component
 *
 * @param label
 * @param id
 * @param error
 * @param props
 */
interface InputProps {
  label: string
  id: string
  error?: string
  hint?: string

  [x: string]: any
}

/**
 * Input component
 * This component is used to render an input field with a label and error message
 * Avoids the need to repeat the same code for each input field
 *
 * @param label
 * @param children
 * @param error
 * @param hint
 * @param props
 * @returns {React.ReactElement} - Rendered component
 */
const Input: React.FC<InputProps> = ({
  label,
  children,
  error,
  hint,
  ...props
}: InputProps): React.ReactElement => {
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label
          htmlFor={props.id}
          className="block text-sm font-medium leading-6 text-gray-900"
        >
          {label}
        </label>
        {children}
      </div>
      <div className="mt-2">
        <input
          {...props}
          className={clsx(
            'px-3 py-1.5',
            'block w-full rounded-md border-0 shadow-sm',
            'ring-1 ring-inset focus:ring-2 focus:ring-inset',
            'text-gray-900 placeholder:text-gray-400',
            'sm:text-sm sm:leading-6',
            {
              'ring-red-600 focus:ring-red-600': error,
              'ring-gray-300 focus:ring-indigo-600': !error,
            },
          )}
        />
        {error && <div className="mt-2 text-sm text-red-500">{error}</div>}
        {hint && <div className="mt-2 text-xs text-gray-500">{hint}</div>}
      </div>
    </div>
  )
}

export default Input
