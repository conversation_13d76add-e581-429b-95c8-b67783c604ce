ingress:
  enabled: true
  annotations:
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'

  basicAuth:
    enabled: true
    username: "favish"
    # Password encoded with Apache MD5 (apr1)
    # echo -n 'replatform!' | openssl passwd -apr1 -stdin
    password: "$apr1$PJZfWa4f$ev26hSXVVubdIi4Hvy6Tr1"


Be careful, if you're using Fastly, you can't use basic auth in charts
  