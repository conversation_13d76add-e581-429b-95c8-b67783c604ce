import type { FC } from 'react'
import BlockHeader from '~/src/components/BlockHeader/BlockHeader'
import SkeletonTable from '~/src/components/SkeletonTable/SkeletonTable'
import type { LondonFixQuery } from '~/src/generated'
import dates from '~/src/utils/dates'
import styles from './LondonFixGrid.module.scss'

const LondonFixGrid: FC<{ data: LondonFixQuery }> = ({ data }) => {
  const dataAsArray = [
    data?.londonFixUSD,
    data?.londonFixEUR,
    data?.londonFixGBP,
  ]

  return (
    <div className={styles.wrapper}>
      <BlockHeader title={'London Fix Price'} />
      <DatesRow ssrTimestamp={data?.londonFixUSD?.results[0]?.timestamp - 1} />
      <MiddleRowAndColumnNames />
      {/* column values */}
      <ul className={styles.listify}>
        {dataAsArray &&
          Object.entries(dataAsArray).map((x: any, idx) => (
            <div key={idx} className={styles.gridify}>
              <h4 className={styles.bold}>
                {(idx === 0 && 'USD') ||
                  (idx === 1 && 'EUR') ||
                  (idx === 2 && 'GBP')}
              </h4>

              <h4>
                {x[1]?.results[0]?.goldAM?.toFixed(2) || <SkeletonTable />}
              </h4>
              <h4>
                {x[1]?.results[0]?.goldPM?.toFixed(2) || <SkeletonTable />}
              </h4>
              <h4>
                {x[1]?.results[0]?.silver?.toFixed(2) || <SkeletonTable />}
              </h4>
              <h4>
                {x[1]?.results[0]?.platinumAM?.toFixed(2) || <SkeletonTable />}
              </h4>
              <h4>
                {x[1]?.results[0]?.platinumPM?.toFixed(2) || <SkeletonTable />}
              </h4>
              <h4>
                {x[1]?.results[0]?.palladiumAM?.toFixed(2) || <SkeletonTable />}
              </h4>
              <h4>
                {x[1]?.results[0]?.palladiumPM?.toFixed(2) || <SkeletonTable />}
              </h4>
            </div>
          ))}
      </ul>
    </div>
  )
}

export default LondonFixGrid

const DatesRow = ({ ssrTimestamp }) => {
  return (
    <div className={styles.gridify}>
      <p>London Fix</p>
      <p className={styles.blank}>
        <span className={styles.dates}>
          Quotes as of {dates.fmtUnix(ssrTimestamp)}
        </span>
      </p>
      <p className={styles.blank} />
      <p />
      <p className={styles.blank}>
        <span className={styles.datesTwo}>
          Quotes as of {dates.fmtUnix(ssrTimestamp)}
        </span>
      </p>
      <p className={styles.blank} />
      <p className={styles.blank} />
      <p />
    </div>
  )
}

const MiddleRowAndColumnNames = () => {
  return (
    <div className={styles.gridify}>
      <p />
      <p className={styles.blank}>
        <span className={styles.forceText}>
          Gold
          <br />
          AM/PM
        </span>
      </p>
      <p />
      <p className="text-center">
        Silver
        <br />
        Noon
      </p>
      <p className={styles.blank}>
        <span className={styles.forceText}>
          Platinum
          <br />
          AM/PM
        </span>
      </p>
      <p />
      <p className={styles.blank}>
        <span className={styles.forceText}>
          Palladium
          <br />
          AM/PM
        </span>
      </p>
      <p />
    </div>
  )
}
