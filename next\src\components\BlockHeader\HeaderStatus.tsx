import clsx from 'clsx'
import type React from 'react'

const HeaderStatus: React.FC<{ isStatus: boolean }> = ({ isStatus }) => {
  return (
    <div className="w-16 space-x-1.5 absolute right-0 h-[34px] top-0 flex items-center justify-center">
      <div className="relative flex flex-col items-center group">
        <span
          className={clsx(
            isStatus ? ' bg-green-500' : 'bg-emerald-800',
            'w-3 h-3 inline-block rounded-full cursor-pointer',
          )}
        />
        <div className="absolute bottom-0 right-[-5px] flex-col items-end hidden mb-6 group-hover:flex w-[200px]">
          <span className="relative z-10 p-2 text-[12px] leading-tight text-white/90 whitespace-no-wrap bg-gray-700 rounded-sm">
            Precious metals spot market is currently active and trading.
          </span>
          <div className="w-3 h-3 -mt-2 rotate-45 bg-gray-700  mr-1"></div>
        </div>
      </div>
      <div className="relative flex flex-col items-center">
        <span className="bg-yellow-600/30 w-3 h-3 inline-block rounded-full cursor-pointer" />
      </div>
      <div className="relative flex flex-col items-center group">
        <span
          className={clsx(
            isStatus ? 'bg-red-800/50' : 'bg-red-500',
            'w-3 h-3  inline-block rounded-full cursor-pointer',
          )}
        />
        <div className="absolute bottom-0 right-[-5px] flex-col items-end mb-6 hidden group-hover:flex w-[240px]">
          <div className="relative z-10 p-2 text-[12px] leading-tight text-white/90 whitespace-no-wrap bg-gray-700 rounded-sm">
            Precious metals spot market is closed.{' '}
          </div>
          <div className="w-3 h-3 -mt-2 rotate-45 bg-gray-700 mr-1"></div>
        </div>
      </div>
    </div>
  )
}
export default HeaderStatus
