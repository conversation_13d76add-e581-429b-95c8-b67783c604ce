import type { UseQueryResult } from '@tanstack/react-query'
import type React from 'react'
import { useEffect, useState } from 'react'
import { useInView } from 'react-intersection-observer'
import { ArticleContent } from '~/src/components-news/Article/ArticleContent'
import type {
  ArticleByAliasQuery,
  NewsArticle,
  NewsByCategoryGenericQuery,
} from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'

/**
 * Article queue content props
 */
interface ArticleQueueContentProps {
  articles: UseQueryResult<NewsByCategoryGenericQuery>
  TYPE_NAME_SUPPORT: string[]
}

/**
 * Article queue content component
 *
 * @param articles - The articles
 * @param TYPE_NAME_SUPPORT - The supported types
 */
const ArticleQueueContent: React.FC<ArticleQueueContentProps> = ({
  articles,
  TYPE_NAME_SUPPORT,
}) => {
  // Intersection observer for infinite scroll
  const { ref, inView } = useInView()

  // Article counter
  const [counter, setCounter] = useState(0)

  // Article queue
  const [articleInQueue, setArticleInQueue] = useState<ArticleByAliasQuery[]>(
    [],
  )

  // Current article url alias (default to the first article)
  const [currentUrlAlias, setCurrentUrlAlias] = useState(
    articles.data ? articles.data[0] : null,
  )

  // Fetch article data
  const { data: article, refetch } = kitcoQuery(
    news.nodeByUrlAlias({
      variables: { urlAlias: currentUrlAlias, auHash: undefined },
      options: {
        enabled: !!currentUrlAlias,
      },
    }),
  )

  /**
   * If the article is in view, add a new article to the queue
   * Record the view of the article
   */
  useEffect(() => {
    if (inView && article) {
      setArticleInQueue((prevArticles) => {
        const articleExists = prevArticles.some(
          (existingArticle) =>
            existingArticle?.nodeByUrlAlias?.id === article?.nodeByUrlAlias?.id,
        )

        if (articleExists) {
          return prevArticles
        }

        return [...prevArticles, article]
      })

      setCounter((prevCounter) => prevCounter + 1)
    }
  }, [inView, article])

  /**
   * Effect to set the current URL alias based on the counter
   */
  useEffect(() => {
    if (articles.data) {
      setCurrentUrlAlias(articles.data[counter])
    }
  }, [articles.data, counter])

  /**
   * Effect to refetch the article data when the URL alias changes
   */
  useEffect(() => {
    if (currentUrlAlias) {
      refetch()
    }
  }, [currentUrlAlias, refetch])

  return (
    <>
      {articleInQueue
        .filter((article: ArticleByAliasQuery) => article !== undefined)
        .map((article: ArticleByAliasQuery, idx) => {
          // Check if the article type is supported
          const isSupportRender: boolean = TYPE_NAME_SUPPORT.includes(
            article?.nodeByUrlAlias?.__typename,
          )

          // If the article type is not supported, return null
          if (!isSupportRender) return <></>

          return (
            <ArticleContent
              key={article?.nodeByUrlAlias?.id || idx}
              counter={idx}
              articleData={article?.nodeByUrlAlias as NewsArticle}
              infiniteScrollArticle={true}
            />
          )
        })}
      <div ref={ref} />
    </>
  )
}

export default ArticleQueueContent
