# ChartJS implementation

The `useC<PERSON><PERSON>s` hook is a custom React hook designed to manage the state and data retrieval for generating charts using the Chart.js library. It utilizes the `react-query` library for data fetching and caching.

## Dependencies
- `dayjs`: A library for handling dates and times in JavaScript.
- `react-query`: A React library for managing state and side effects.

## Usage
```javascript
import { useChartJs } from "./path/to/useChartJs"; // Adjust the path accordingly
```

## Signature
`function useChartJs(args: { symbol: string; currency: string });`

## Parameters
args: An object containing symbol and currency properties representing the financial symbol and currency for which the chart data is requested.

## State
The hook maintains the following state variables:
- scale: A string representing the time scale of the chart ("5m", "1h", or "1d").
- range: An array of Unix timestamps representing the time range for which the chart data is fetched.

## Functions
`setTimescaleFiveMin`
Sets the time scale to 5 minutes and updates the range accordingly.

`setTimescaleOneHour``
Sets the time scale to 1 hour and updates the range accordingly.

`setTimescaleOneDay`
Sets the time scale to 1 day and updates the range accordingly.

`setRange`
Sets the time scale based on the provided parameter and updates the range accordingly.

## Data Fetching
The hook uses the useQuery function from react-query to fetch chart data. It queries the metals.nivoChartData function with specific parameters, including the start and end times determined by the selected time scale.

## Memoized Data Transformation
The hook memoizes the transformation of raw data received from the API into a format suitable for rendering a chart using Chart.js. It processes historical data and organizes it into labels and corresponding values.

## Return Values
The hook returns an object with the following properties:
`formedData``: An object containing labels and values arrays suitable for rendering a Chart.js chart.
`nowData`: Real-time data received from the API.
`scale`: The current time scale selected.
`setRange`: A function to set the time scale based on user selection.

## Example
```javascript
const { formedData, nowData, scale, setRange } = useChartJs({ symbol: "AAPL", currency: "USD" });
```