import type React from 'react'
import { FaCheck } from 'react-icons/fa6'

/**
 * CheckboxProps interface
 * Define the props for the Checkbox component
 *
 * @param label
 * @param id
 * @param checked
 * @param onChange
 * @param error
 * @param props
 */
interface CheckboxProps {
  label?: string
  id: string
  checked: boolean
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  error?: string
  children?: React.ReactNode

  [x: string]: any
}

/**
 * Checkbox component
 * This component renders a checkbox input with a label and an optional error message
 *
 * @param label
 * @param id
 * @param checked
 * @param onChange
 * @param error
 * @param props
 * @returns {React.ReactElement} - Componente renderizado
 */
const Checkbox: React.FC<CheckboxProps> = ({
  label,
  id,
  checked = false,
  onChange,
  error,
  children,
  ...props
}: CheckboxProps): React.ReactElement => {
  return (
    <>
      <div className="inline-flex items-start">
        <label
          className="relative flex cursor-pointer items-center rounded-full"
          htmlFor={id}
        >
          <input
            type="checkbox"
            className="border-blue-gray-200 before:bg-blue-gray-500 peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border transition-all before:absolute before:left-2/4 before:top-2/4 before:block before:h-12 before:w-12 before:-translate-x-2/4 before:-translate-y-2/4 before:rounded-full before:opacity-0 before:transition-opacity before:content-[''] checked:border-gray-900 checked:bg-gray-900 checked:before:bg-gray-900 hover:before:opacity-10"
            id={id}
            checked={checked}
            onChange={onChange}
            {...props}
          />
          <span className="pointer-events-none absolute left-2/4 top-2/4 -translate-x-2/4 -translate-y-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
            <FaCheck />
          </span>
        </label>
        <label
          className="ml-2 mt-px cursor-pointer select-none font-light text-gray-700"
          htmlFor={id}
        >
          {children || label}
        </label>
      </div>
      {error && <div className="mt-2 text-sm text-red-500">{error}</div>}
    </>
  )
}

export default Checkbox
