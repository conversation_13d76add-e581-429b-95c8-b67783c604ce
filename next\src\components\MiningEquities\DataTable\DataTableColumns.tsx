import type { ColumnDef, Row, SortingFn } from '@tanstack/react-table'
import { clsx } from 'clsx'
import Link from 'next/link'
import DateTime from '~/src/components/DateTime/DateTime'
import CategoryTag from '~/src/components/MiningEquities/DataTable/CategoryTag'
import PriceChange from '~/src/components/Price/PriceChange'
import type MiningEquity from '~/src/types/DataTable/MiningEquity'
import { titleCase } from '~/src/utils/titleCase'

const desktopMeta = {
  classNameHeader: 'hidden sm:table-cell',
  classNameHeaderDiv: '',
  classNameCell: 'hidden sm:table-cell',
}
const mobileMeta = {
  classNameHeader: 'hidden ',
  classNameHeaderDiv: '',
  classNameCell: 'table-cell sm:hidden ',
}

/**
 * Custom sorting function for company names and symbol.
 *
 * This function prioritizes names starting with numbers over those starting with letters.
 * It uses case-insensitive comparison and sorts alphabetically for strings starting with the same type of characters.
 *
 * @param {Row<any>} rowA - The first row to compare.
 * @param {Row<any>} rowB - The second row to compare.
 * @param {string} columnId - The ID of the column being sorted.
 * @returns {number} - Returns:
 *   - A negative number if `rowA` should come before `rowB`.
 *   - A positive number if `rowA` should come after `rowB`.
 *   - Zero if they are equal.
 */
const customSort: SortingFn<MiningEquity> = (
  rowA: Row<MiningEquity>,
  rowB: Row<MiningEquity>,
  columnId: string,
): number => {
  // Extract values from the rows
  const nameA = rowA.getValue(columnId).toString()
  const nameB = rowB.getValue(columnId).toString()

  // Normalize the values for case-insensitive comparison
  const normalizedA = nameA.trim().toLowerCase()
  const normalizedB = nameB.trim().toLowerCase()

  // Check if the values start with numbers
  const isNumberA = /^\d/.test(normalizedA)
  const isNumberB = /^\d/.test(normalizedB)

  // Handle cases where one starts with a number and the other doesn't
  if (isNumberA && !isNumberB) return -1 // Numbers go before letters
  if (!isNumberA && isNumberB) return 1 // Letters go after numbers

  // If both are numbers or both are letters, sort alphabetically
  return normalizedA.localeCompare(normalizedB)
}

/**
 * Custom sorting function to prioritize `ChangePercentageVal` and then `ChangeVal`.
 *
 * @param {Row<any>} rowA - The first row to compare.
 * @param {Row<any>} rowB - The second row to compare.
 * @param {string} columnId - The ID of the column being sorted (not used in this specific case).
 * @returns {number} - Returns:
 *   - A negative number if `rowA` should come before `rowB`.
 *   - A positive number if `rowA` should come after `rowB`.
 *   - Zero if they are equal.
 */
const sortByChangePercentageAndChange: SortingFn<any> = (
  rowA: Row<any>,
  rowB: Row<any>,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- This is used in the generic sorting function
  columnId: string,
): number => {
  // Get the values for ChangePercentageVal and ChangeVal
  const percentageA = rowA.original.ChangePercentageVal
  const percentageB = rowB.original.ChangePercentageVal
  const changeA = rowA.original.ChangeVal
  const changeB = rowB.original.ChangeVal

  // Primary sort: ChangePercentageVal
  if (percentageA !== percentageB) {
    return percentageB - percentageA // Descending order
  }

  // Secondary sort: ChangeVal
  return changeB - changeA // Descending order
}

const columns: ColumnDef<MiningEquity>[] = [
  // --- DESKTOP COLUMNS ---
  {
    accessorKey: 'Name',
    id: 'Name',
    header: 'Company',
    sortingFn: customSort,
    cell: (info) => {
      const value = info.row.original
      return (
        <div className="sticky flex flex-col items-start justify-center gap-1 py-2">
          <Link
            href={value.SymbolURL}
            className="font-['Mulish'] text-sm font-bold leading-normal text-neutral-900 truncate"
            title={titleCase(value.Name)}
            target="_blank"
          >
            {titleCase(value.Name, { locale: 'en' })}
          </Link>
          <CategoryTag value={value.Category} />
        </div>
      )
    },
    enablePinning: true,
    size: 250,
    meta: desktopMeta,
  },
  {
    accessorKey: 'TVSymbol',
    id: 'TVSymbol',
    header: 'Symbol',
    sortingFn: customSort,
    cell: (info) => {
      const value = info.row.original
      return (
        <div className="flex flex-col items-start justify-center gap-1">
          <div className="font-['Mulish'] text-sm leading-none text-neutral-900">{value.TVSymbol}</div>
          <div className="text-neutral-500 text-xs">{value.Exchange}</div>
        </div>
      )
    },
    size: 100,
    meta: desktopMeta,
  },
  {
    accessorKey: 'PriceVal',
    id: 'Price',
    header: 'Price',
    cell: (info) => {
      const value = info.row.original
      const priceValue = typeof value?.PriceVal === 'string' ? parseFloat(value.PriceVal) : value?.PriceVal

      const formattedPrice = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: value?.Currency || 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(priceValue || 0)

      return (
        <div className="flex flex-col items-start justify-center gap-1 self-stretch py-1.5">
          <div className="whitespace-nowrap font-['Mulish'] text-sm font-bold leading-none text-neutral-700">
            {formattedPrice}
          </div>
          <div className="flex font-['Mulish'] text-xs font-normal leading-none text-zinc-600 text-nowrap">
            <DateTime
              timeZone="US/Eastern"
              timeFormat="MMM dd, HH:mm"
              date={value?.Timestamp}
            />
          </div>
        </div>
      )
    },
    size: 150,
    meta: {
      ...desktopMeta,
      classNameHeaderDiv: 'w-full justify-start',
    },
  },
  {
    accessorKey: 'ChangePercentageVal',
    id: 'Change',
    header: 'Change',
    sortingFn: sortByChangePercentageAndChange,
    cell: (info) => {
      const value = info.row.original
      return (
        <PriceChange
          className="flex grow basis-0 items-center justify-start gap-1"
          value={value}
          changeKey="Change"
          changeValKey="ChangeVal"
          percentageKey="ChangePercentage"
          percentageValKey="ChangePercentageVal"
          symbol=""
        />
      )
    },
    size: 200,
    meta: {
      ...desktopMeta,
      classNameHeaderDiv: 'justify-start',
    },
  },
  // --- MOBILE COLUMN ---
  {
    accessorKey: 'Name',
    id: 'Mobile',
    header: 'Mobile',
    cell: (info) => {
      const value = info.row.original
      const priceValue = typeof value?.PriceVal === 'string' ? parseFloat(value.PriceVal) : value?.PriceVal

      const formattedPrice = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: value?.Currency || 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(priceValue || 0)

      return (
        <div className="flex flex-col w-full py-2 gap-2">
          {/* Top Row: Name, Symbol, Price, Change */}
          <div className="flex justify-between items-start">
            {/* Left side: Name and Symbol */}
            <div className="flex flex-col items-start gap-1">
              <Link href={value.SymbolURL} className="font-['Mulish'] text-sm font-bold text-neutral-900" target="_blank">
                {titleCase(value.Name)}
              </Link>
              <div className="font-['Mulish'] text-xs text-neutral-500">{value.TVSymbol}</div>
            </div>
            {/* Right side: Price and Change */}
            <div className="flex flex-col items-end gap-1">
              <div className="whitespace-nowrap font-['Mulish'] text-sm font-bold text-neutral-700">
                {formattedPrice}
              </div>
              <PriceChange
                value={value}
                changeKey="Change"
                changeValKey="ChangeVal"
                percentageKey="ChangePercentage"
                percentageValKey="ChangePercentageVal"
                symbol=""
              />
            </div>
          </div>
          {/* Bottom Row: Tags and Timestamp */}
          <div className="flex justify-between items-center">
            <CategoryTag value={value.Category} />
            <div className="flex font-['Mulish'] text-xs font-normal text-zinc-600">
              <DateTime
                timeZone="US/Eastern"
                timeFormat="MMM dd, yyyy HH:mm"
                date={value?.Timestamp}
              />
            </div>
          </div>
        </div>
      )
    },
    enablePinning: true,
    size: 430,
    meta: mobileMeta,
  },
  {
    accessorKey: 'VolumeVal',
    id: 'Volume',
    header: 'Volume',
    cell: (info) => {
      const value = info.row.original
      return (
        <div className="flex items-center justify-start">
          <div className="font-['Mulish'] text-sm leading-none text-neutral-900">
            {value.Volume}
          </div>
        </div>
      )
    },
    size: 75,
    meta: {
      ...desktopMeta,
      classNameHeaderDiv: 'justify-start',
    },
  },
  {
    accessorKey: 'HighVal',
    id: 'High',
    header: 'High',
    cell: (info) => {
      const value = info.row.original
      return (
        <div className="flex items-center justify-start">
          <div className="font-['Mulish'] text-sm leading-none text-neutral-900">
            {value.High}
          </div>
        </div>
      )
    },
    size: 75,
    meta: {
      ...desktopMeta,
      classNameHeaderDiv: 'justify-start',
    },
  },
  {
    accessorKey: 'LowVal',
    id: 'Low',
    header: 'Low',
    cell: (info) => {
      const value = info.row.original
      return (
        <div className="flex items-center justify-start">
          <div className="font-['Mulish'] text-sm leading-none text-neutral-900">
            {value.Low}
          </div>
        </div>
      )
    },
    size: 75,
    meta: {
      ...desktopMeta,
      classNameHeaderDiv: 'justify-start',
    },
  },
]

export default columns
