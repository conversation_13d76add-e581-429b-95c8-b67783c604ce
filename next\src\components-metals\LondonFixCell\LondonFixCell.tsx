import dayjs from 'dayjs'
import { Suspense } from 'react'
import LondonFixGrid from '~/src/components-metals/LondonFixGrid/LondonFixGrid'
import LondonFixGridMobile from '~/src/components-metals/LondonFixGrid/LondonFixGridMobile'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import { Query } from '~/src/components/Query/Query'
import type { LondonFixQuery } from '~/src/generated'
import { metals } from '~/src/lib/metals-factory.lib'
import useScreenSize from '~/src/utils/useScreenSize'

/**
 * LondonFixCell component
 *
 * @returns {JSX.Element} - The rendered component
 * @constructor
 */
function LondonFixCell() {
  // Fetch London Fix data
  const fetcher = metals.londonFix({
    variables: {
      year: dayjs().year().toString(),
    },
    options: {},
  })

  const { isTablet, isMobile } = useScreenSize()

  return (
    <ErrBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Query fetcher={fetcher}>
          {({ data }) => {
            if (isTablet || isMobile) {
              return <LondonFixGridMobile data={data as LondonFixQuery} />
            }

            return <LondonFixGrid data={data as LondonFixQuery} />
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}

export default LondonFixCell
