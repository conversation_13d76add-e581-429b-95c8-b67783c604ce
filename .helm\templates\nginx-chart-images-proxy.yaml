apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-chart-images-proxy
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginx-chart-images-proxy
  template:
    metadata:
      labels:
        app: nginx-chart-images-proxy
    spec:
      containers:
        - name: nginx
          image: nginx:alpine
          volumeMounts:
            - name: nginx-config-volume
              mountPath: /etc/nginx/conf.d
          livenessProbe:
            httpGet:
              path: /hc
              port: 80
            initialDelaySeconds: 15
            periodSeconds: 10
            failureThreshold: 5
          resources:
            requests:
              cpu: "0.25"
              memory: 1Gi
              ephemeral-storage: "1Gi"
            limits:
              cpu: "0.5"
              memory: 2Gi
              ephemeral-storage: "10Gi"
      volumes:
        - name: nginx-config-volume
          configMap:
            name: nginx-chart-images-cm
