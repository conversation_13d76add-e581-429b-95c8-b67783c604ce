import type { ZxcvbnResult } from '@zxcvbn-ts/core'
import type React from 'react'
import { useEffect, useState } from 'react'
import Input from '~/src/components/Auth/Form/Elements/Input'
import Validator from '~/src/features/auth/validator'

/**
 * NewPasswordProps interface
 * Define the props for the NewPassword component
 *
 * @param onChange
 * @param onError
 */
interface NewPasswordProps {
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  onError?: (hasErrors: boolean) => void
}

/**
 * Errors for the RecoverForumUserForm component.
 */
interface NewPasswordErrors {
  password?: string | undefined
  confirm_password?: string | undefined
}

/**
 * NewPassword component
 * This component renders a newPassword input with a label and an optional error message
 *
 * @param onChange
 * @param onError
 * @returns {React.ReactElement} - Rendered component
 */
const NewPassword: React.FC<NewPasswordProps> = ({
  onChange,
  onError,
}: NewPasswordProps): React.ReactElement => {
  // Form fields
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  // Using an object to track errors for each field
  const [errors, setErrors] = useState<NewPasswordErrors>({})

  // Password strength score
  const [passwordStrengthScore, setPasswordStrengthScore] = useState(0)

  /**
   * Call the onError function when the errors change
   */
  useEffect(() => {
    // Check if there are any errors
    onError(Object.values(errors).some((error) => error !== undefined))
  }, [errors])

  /**
   * Validate the confirm password field when the password changes
   */
  useEffect(() => {
    validateField('confirm_password', confirmPassword)
  }, [password])

  /**
   * Validate a field based on the field name and value
   * This function is used to validate each field in the form
   * It sets the error message for the field if any
   * It also returns a boolean value to indicate if the field is valid
   * This function is called on field change
   *
   * @param fieldName
   * @param value
   * @param setError
   */
  const validateField = (fieldName: string, value: string, setError = true) => {
    // Trim the value
    const newValue = value.trim()

    // Initialize the error message
    let errorMessage: string | undefined

    // Initialize the password errors and result
    let passwordErrors: string[] = []

    // Initialize the password strength
    let result: { errors: string[]; passwordStrength: ZxcvbnResult } | undefined

    // Initialize the password strength score
    let passwordStrength: ZxcvbnResult

    switch (fieldName) {
      case 'password':
        // Check if the password are the same

        // Validate the password length
        result = Validator.validatePassword(newValue, true)

        // Separate the errors and password strength
        passwordErrors = result.errors
        passwordStrength = result.passwordStrength

        // Set the error message
        errorMessage = passwordErrors.length > 0 ? passwordErrors[0] : undefined

        // Set the password strength score
        setPasswordStrengthScore(passwordStrength.score)

        // Check if the password is too weak
        if (passwordErrors.length <= 0 && passwordStrength.score < 2) {
          errorMessage = passwordStrength.feedback.warning
            ? passwordStrength.feedback.warning
            : 'Password is too weak. Try combining uppercase, lowercase, numbers, and special characters.'
        }
        break
      case 'confirm_password':
        errorMessage = !Validator.passwordsMatch(password, newValue)
          ? 'Passwords do not match.'
          : undefined
        break
      default:
        errorMessage = undefined
    }

    // If setError is false, return the validation result
    if (!setError) {
      return errorMessage === undefined
    }

    // Set the error message for the field
    setErrors((prevErrors) => ({
      ...prevErrors,
      [fieldName]: errorMessage,
    }))
  }

  /**
   * Handle change event for input fields
   * This function is called when the value of an input field changes
   * It updates the state of the corresponding field
   * It also calls the validateField function to validate the field
   *
   * @param e
   */
  const handleChange = (e) => {
    // Get the field name and value
    const fieldName = e.target.id
    const value = e.target.value.toString().trim()

    // Update the field state
    switch (fieldName) {
      case 'password':
        setPassword(value)
        onChange(e)
        break
      case 'confirm_password':
        setConfirmPassword(value)
        break
    }

    // Validate the field
    validateField(fieldName, value)
  }

  return (
    <>
      {/* Password input */}
      <div>
        <Input
          label="Password"
          id="password"
          name="password"
          type="password"
          autoComplete="new-password"
          required={true}
          value={password}
          onChange={handleChange}
          error={errors.password}
        />
      </div>

      {/* Password strength indicator */}
      {password && password.length > 0 && (
        <div className="flex flex-col gap-2">
          <div className="h-2.5 w-full bg-gray-200">
            <div
              className={`h-2.5 ${
                passwordStrengthScore === 0
                  ? 'bg-gray-300' // Grey for 0 score
                  : passwordStrengthScore < 2
                    ? 'bg-red-500' // Red for low score
                    : passwordStrengthScore < 4
                      ? 'bg-yellow-500' // Yellow for medium score
                      : 'bg-green-500' // Green for high score
              }`}
              style={{ width: `${(passwordStrengthScore || 0) * 25}%` }}
            />
          </div>

          <span className="text-sm text-gray-500">
            Password Security:{' '}
            {passwordStrengthScore === 0
              ? 'Unsafe'
              : passwordStrengthScore < 2
                ? 'Weak'
                : passwordStrengthScore < 4
                  ? 'Medium'
                  : 'Strong'}
          </span>
        </div>
      )}

      {/* Confirm Password input */}
      <div>
        <Input
          label="Confirm Password"
          id="confirm_password"
          name="confirm_password"
          type="password"
          autoComplete="new-password"
          required={true}
          value={confirmPassword}
          onChange={handleChange}
          error={errors.confirm_password}
        />
      </div>
    </>
  )
}

export default NewPassword
