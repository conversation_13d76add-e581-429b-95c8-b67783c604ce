.dragHandle {
  cursor: grab;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 100%;
  opacity: 0.5;
  transition: opacity 0.2s ease-in-out;
}

.dragHandle:hover {
  opacity: 1;
}

.dragHandle:active {
  cursor: grabbing;
}

.row {
  transition: var(--transition, transform 0.2s ease);
  position: relative;
  z-index: 1;
  transform: var(--transform, none);
}

.rowDragging {
  opacity: 0.8;
  background-color: rgba(0, 0, 0, 0.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.dragIcon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}
