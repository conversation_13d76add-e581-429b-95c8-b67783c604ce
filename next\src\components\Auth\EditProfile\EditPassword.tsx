const EditPassword = () => {
  return (
    <>
      <h3 className="mx-0 mb-4 mt-0 border-0 border-solid text-xl font-bold leading-7">
        Password information
      </h3>
      <form action="#" className="border-0 border-solid">
        <div className="grid grid-cols-6 gap-6 border-0 border-solid">
          <div className="col-span-6 border-0 border-solid sm:col-span-3">
            <label
              htmlFor="current-password"
              className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
            >
              Current password
            </label>
            <input
              type="text"
              name="current-password"
              id="current-password"
              className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
              placeholder="••••••••"
            />
          </div>
          <div className="col-span-6 border-0 border-solid sm:col-span-3">
            <label
              htmlFor="new-password"
              className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
            >
              New password
            </label>
            <input
              type="text"
              name="new-password"
              id="new-password"
              className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
              placeholder="••••••••"
            />
          </div>
          <div className="col-span-6 border-0 border-solid sm:col-span-3">
            <label
              htmlFor="confirm-password"
              className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
            >
              Confirm password
            </label>
            <input
              type="text"
              name="confirm-password"
              id="confirm-password"
              className="m-0 block w-full cursor-text appearance-none rounded-lg border border-solid border-gray-300 bg-gray-50 p-2 text-base text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
              placeholder="••••••••"
            />
          </div>
          <div className="col-span-full border-0 border-solid">
            <div className="border-0 border-solid text-sm font-medium leading-5">
              Password requirements:
            </div>
            <div className="mb-1 border-0 border-solid text-sm font-normal leading-5 text-gray-500">
              Ensure that these requirements are met:
            </div>
            <ul className="m-0 border-0 border-solid py-0 pl-4 pr-0 text-gray-500">
              <li className="border-0 border-solid text-left text-xs font-normal leading-4">
                At least 10 characters (and up to 100 characters)
              </li>
              <li className="mb-0 mt-1 border-0 border-solid text-left text-xs font-normal leading-4">
                At least one lowercase character
              </li>
              <li className="mb-0 mt-1 border-0 border-solid text-left text-xs font-normal leading-4">
                Inclusion of at least one special character, e.g., ! @ # ?
              </li>
              <li className="mb-0 mt-1 border-0 border-solid text-left text-xs font-normal leading-4">
                Some text here zoltan
              </li>
            </ul>
          </div>
          <div className="col-span-6 border-0 border-solid">
            <button
              className="m-0 cursor-pointer rounded-lg border-0 border-solid bg-blue-700 bg-none px-5 py-2 text-center text-sm font-medium normal-case leading-5 text-white"
              type="submit"
            >
              Save all
            </button>
          </div>
        </div>
      </form>
    </>
  )
}

export default EditPassword
