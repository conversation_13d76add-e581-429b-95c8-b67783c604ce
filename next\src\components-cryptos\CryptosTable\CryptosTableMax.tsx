import clsx from 'clsx'
import currency from 'currency.js'
import Link from 'next/link'
import * as Tables from '~/src/components/Tables/Tables'
import type { CryptoComparePriceFull, CryptosTableQuery } from '~/src/generated'
import { allCryptos } from '~/src/lib/all-cryptos'
import { cryptos } from '~/src/lib/cryptos-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { colorizeTable } from '~/src/utils/colorize-change.util'
import { currencyFmt } from '~/src/utils/currencyFmt'

interface Item
  extends Pick<
    CryptoComparePriceFull,
    | 'fromSymbol'
    | 'changePct24HourCalculated'
    | 'changePctHourCalculated'
    | 'changePct7DayCalculated'
    | 'mktCap'
    | 'volumeDay'
    | 'imageUrl'
    | 'price'
    | 'totalVolume24h'
    | 'totalVolume24hTo'
  > {
  name: string
  href: string
}

const headerColumns = [
  { id: 1, name: 'Coin' },
  { id: 2, name: 'Price' },
  { id: 3, name: '1h%' },
  { id: 4, name: '24h%' },
  { id: 5, name: '7d%' },
  { id: 6, name: 'Volume (24h)' },
  { id: 7, name: 'Market Cap' },
]

export const cryptosTableMaxVariables = {
  symbols: allCryptos.map((item) => item.symbol).join(','),
  currency: 'USD',
}

function useCryptosTableMax() {
  const { data } = kitcoQuery(
    cryptos.cryptosTable({
      variables: cryptosTableMaxVariables,
      options: {
        // @ts-ignore
        select: (res: CryptosTableQuery) => {
          const hashMap = new Map(
            allCryptos.map((item) => [item.symbol, item.name]),
          )
          const hashHrefs = new Map(
            allCryptos.map((item) => [item.symbol, item.href]),
          )

          return res?.GetCryptoComparePriceFullV3?.map((item) => {
            if (!item?.price && !item?.fromSymbol) return null

            return {
              ...item,
              name: hashMap.get(item.fromSymbol),
              href: hashHrefs.get(item.fromSymbol),
            }
          }).filter(Boolean)
        },
      },
    }),
  )

  // typescript is mega upset about the transformation above, so let's just alias
  return data as Item[]
}

// scaffold a nextjs page
export function CryptosTableMax() {
  const transformedItems = useCryptosTableMax()

  return (
    <div className="relative border border-[#E2E8F0]">
      <Tables.Root label="Cryptocurrencies data">
        <Tables.Header>
          {headerColumns.map((x, idx) => (
            <Tables.ColumnHeader key={x.id} index={x.id}>
              <span
                className={clsx(
                  'whitespace-nowrap',
                  idx === headerColumns.length - 1 ? 'px-2' : '',
                )}
              >
                {x.name}
              </span>
            </Tables.ColumnHeader>
          ))}
        </Tables.Header>
        <Tables.Body>
          {transformedItems?.map((x, idx) => (
            <Tables.Row index={idx} key={x.fromSymbol}>
              <Tables.RowLabelCell index={idx}>
                <Link
                  href={x.href ?? '/'}
                  className="absolute flex h-full w-full items-center tablet:static"
                >
                  <div className="absolute flex items-center">
                    <img
                        src={/^https?:\/\//i.test(x.imageUrl)
                            ? x.imageUrl
                            : `https://cryptocompare.com/${x.imageUrl}`}
                      alt={x.name}
                      className="mx-2 h-6 w-6"
                    />
                    <div>
                      <p className="font-semibold text-[#121212] group-hover:text-[#1D61AE] group-hover:underline [group-&[data-selected]]/row:bg-yellow-500">
                        {x.name}
                      </p>
                      <p className="text-black/40">{x.fromSymbol}</p>
                    </div>
                  </div>
                </Link>
              </Tables.RowLabelCell>
              <Tables.NumberCell>
                <span className="pl-[145px] text-[#121212] tablet:pl-0">
                  {currency(x.price).format()}
                </span>
              </Tables.NumberCell>
              <Tables.NumberCell>
                <ColorSpan value={x.changePctHourCalculated} />
              </Tables.NumberCell>
              <Tables.NumberCell>
                <ColorSpan value={x.changePct24HourCalculated} />
              </Tables.NumberCell>
              <Tables.NumberCell>
                <ColorSpan value={x.changePct7DayCalculated} />
              </Tables.NumberCell>
              <Tables.NumberCell>
                <span className="text-[#121212]">
                  {currencyFmt(x?.totalVolume24hTo)}
                </span>
              </Tables.NumberCell>
              <Tables.NumberCell>
                <span className="text-[#121212]">{currencyFmt(x?.mktCap)}</span>
              </Tables.NumberCell>
            </Tables.Row>
          ))}
        </Tables.Body>
      </Tables.Root>
    </div>
  )
}

const ColorSpan = (props: { value: number }) => (
  <span className={clsx(colorizeTable(props.value))}>
    {props.value.toFixed(2)}
  </span>
)
