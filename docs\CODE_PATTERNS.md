## Styling / css
The codebase has both `.module.scss` and tailwindcss available. Use whichever you feel most comfortable with. :)

## Tables
Please use this library. it's kinda sick. 
https://react-spectrum.adobe.com/react-aria/Table.html
https://github.com/favish/kitco-cms-next/blob/dev/next/src/components-cryptos/CryptosTable/CryptosTable.tsx#L56

## Organization
I think it's important to think of this project by data types, which are
- News (Opinions, Street talk, etc..)
- Metals (Precious metals like gold, silver, etc..)
- Markets (Stocks, indices, futures, etc..)


## Data SSR/CSR
We're using react-query, it handles clientside fetching as well as data state. As we fetch, we create a store/cache that is a KV.
Generally, the key is composed of the query name + the variables and the value is the network response.
During SSR, we create a client and make fetches. here's an example:
```
export const getServerSideProps = async () => {
  const { dehydratedState } = await ssrQueries({
    queries: [
      metals.londonFixDynamic({
        variables: {
          currency: "USD",
          startTime: roundTimestampFromArg(
            dayjs(currentTimestamp()).subtract(1, "week").unix(),
          ),
          endTime: currentTimestamp(),
        },
      }),
    ],
  });

  return {
    props: {
      dehydratedState,
    },
  };
};
```

### So what's this code doing?
1. metals.londonFix is wrapping a query key with the variables, and a query FN.
    - https://github.com/favish/kitco-cms-next/blob/dev/next/src/lib/metals-factory.lib.ts#L685

2. The `dehydratedState` object is storing all of the responses for the list of queries.

3. We pass that `dehydratedState` object into react-query that exists on the client. 
    - This enables us to use react-query as a stateful object; if a query key has a value, the client wont need to fetch it. 

## Charts and historical data representation


