import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import Link from 'next/link'
import MarketStatus from '~/src/components/BidAskGrid/MarketStatus'
import cs from '~/src/utils/cs'
import isNegative from '~/src/utils/isNegative'
import HeaderStatus from '../BlockHeader/HeaderStatus'
import SkeletonTable from '../SkeletonTable/SkeletonTable'
import styles from './BidAskGridMobile.module.scss'

dayjs.extend(utc)
dayjs.extend(timezone)

interface Props {
  title: string
  values: any
  isBaseMetals?: boolean
  isStatus?: boolean
}

const BidAskGridMobile = ({
  title,
  values,
  isBaseMetals = false,
  isStatus,
}: Props) => {
  const metalTitle = (metal: any) => {
    if (metal?.name?.toUpperCase() === 'RHODIUM') return metal?.name

    const urlMetals = isBaseMetals
      ? `/price/base-metals/${metal?.name?.toLowerCase()}`
      : `/charts/${metal?.name?.toLowerCase()}`
    return (
      <Link href={urlMetals} className={styles.bold}>
        {metal?.name || <SkeletonTable />}
      </Link>
    )
  }

  return (
    <div className={styles.borderWrapper}>
      <BlockHeader title={title} isStatus={isStatus} />
      <MarketStatus />
      <div className={cs([styles.gridder, styles.titlesBorder])}>
        <p>METAL</p>
        <p>LAST</p>
        <p>CHANGE</p>
      </div>
      <ul className={styles.listify}>
        {values.map((metal, idx: number) => (
          <li
            className={
              !(idx % 2) ? styles.item : cs([styles.item, styles.altBg])
            }
            key={metal?.symbol ?? idx}
          >
            <div className={styles.gridder}>
              <span className={styles.bold}>{metalTitle(metal)}</span>
              <span>
                {!metal?.results ? (
                  <SkeletonTable />
                ) : (
                  metal?.results.map((x) => x.bid.toFixed(isBaseMetals ? 4 : 2))
                )}
              </span>
              <div className={styles.changeRow}>
                <div className={styles.dollarChange}>
                  {!metal ? (
                    <SkeletonTable />
                  ) : (
                    <>
                      {metal?.results?.map((x) => (
                        <span
                          key={idx}
                          className={cs([
                            isNegative(x.change)
                              ? styles.changeDown
                              : styles.changeUp,
                          ])}
                        >
                          {metal?.results.map((x) =>
                            x?.change.toFixed(isBaseMetals ? 3 : 2),
                          )}{' '}
                          {'('}
                          {metal?.results.map((x) =>
                            x?.changePercentage.toFixed(2),
                          )}
                          {'%'}
                          {')'}
                        </span>
                      ))}
                    </>
                  )}
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
      {/* <div className={styles.linkContainer}>
        <Link href="/charts">View Precious Metals</Link>
      </div> */}
    </div>
  )
}

interface BlockHeaderProps {
  title: string
  href?: string
  isStatus?: boolean
}
const LinkComponent: React.FC<{ href: string; children: React.ReactNode }> = ({
  href,
  children,
}) => (
  <Link href={href}>
    <h2 className="text-base font-semibold capitalize text-white">
      {children}
    </h2>
  </Link>
)

const BlockHeader: React.FC<BlockHeaderProps> = ({ title, href, isStatus }) => {
  return (
    <div className="relative py-2 px-4 text-center text-white bg-[#373737]">
      {href ? (
        <LinkComponent href={href}>{title}</LinkComponent>
      ) : (
        <>
          <h2 className="text-base font-semibold capitalize">{title}</h2>
          <HeaderStatus isStatus={isStatus} />
        </>
      )}
    </div>
  )
}

export default BidAskGridMobile
