import type { FC } from 'react'
import ItemRow from '~/src/components/BidAskGrid/ItemRow'
import ItemRowSkeleton from '~/src/components/BidAskGrid/ItemRowSkeleton'
import MarketStatus from '~/src/components/BidAskGrid/MarketStatus'
import WeightType from '~/src/types/WeightSelect/WeightType'
import cs from '~/src/utils/cs'
import { CurrencySelect } from '../CurrencySelect'
import { DynamicWeightSelect } from '../WeightSelect'
import styles from './BidAskGrid.module.scss'
import HeaderStatus from './HeaderStatus'

/**
 * Props for the BidAskGrid component.
 *
 * @interface BidAskGridProps
 * @property {string} title - The title of the component.
 * @property {any} data - The data to be displayed.
 * @property {boolean} isLoading - Whether the data is loading.
 * @property {boolean} isBaseMetals - Whether the component is for base metals.
 * @property {boolean} isStatus - Whether the component is for market status.
 * @constructor
 */
interface BidAskGridProps {
  title: string
  data: any
  isLoading: boolean
  isBaseMetals?: boolean
  isStatus?: boolean
}

/**
 * BidAskGrid component.
 * This component is used to display the bid and ask prices for a given metal.
 *
 * @param {string} title - The title of the component.
 * @param {any} data - The data to be displayed.
 * @param {boolean} isLoading - Whether the data is loading.
 * @param {boolean} isBaseMetals - Whether the component is for base metals.
 * @param {boolean} isStatus - Whether the component is for market status.
 * @returns {JSX.Element} - The rendered component.
 * @constructor
 */
const BidAskGrid: FC<BidAskGridProps> = ({
  title,
  data,
  isLoading,
  isBaseMetals = false,
  isStatus,
}: BidAskGridProps) => {
  return (
    <div>
      <div className="relative">
        <h2
          className={cs([
            'text-center text-base font-semibold capitalize',
            styles.header,
          ])}
        >
          {title}
        </h2>
        <HeaderStatus isStatus={isStatus} />
      </div>
      <div className={styles.flexSpaceBetween}>
        <div className="dropdownsContainer grid w-full grid-cols-3">
          <div className="flex flex-1 items-center gap-4">
            <CurrencySelect />
            <DynamicWeightSelect
              type={
                isBaseMetals ? WeightType.BaseMetals : WeightType.PreciousMetals
              }
            />
          </div>
          <MarketStatus />
          <div className="flex-1" />
        </div>
      </div>
      <div className="overflow-x-auto">
        <div className={styles.gridifier}>
          <p>Metals</p>
          <p>Date</p>
          <p>
            Time <br />
            (EST)
          </p>
          <p>Bid</p>
          <p>Ask</p>
          <p>Change</p>
          <p>Low</p>
          <p>High</p>
        </div>
        <ul
          className={cs([
            styles.listify,
            !isLoading ? 'undefined' : 'opacity-50',
          ])}
        >
          {data[0] === undefined
            ? data?.map((_: number) => <ItemRowSkeleton key={_} />)
            : data?.map((x, idx: number) => (
                <ItemRow
                  ask={x?.results[0]?.ask}
                  bid={x?.results[0]?.bid}
                  change={x?.results[0]?.change}
                  changePercentage={x?.results[0]?.changePercentage}
                  high={x?.results[0]?.high}
                  low={x?.results[0]?.low}
                  name={x?.name}
                  timestamp={x?.results[0]?.originalTime}
                  key={idx}
                  isBaseMetals={isBaseMetals}
                />
              ))}
        </ul>
      </div>
    </div>
  )
}

export default BidAskGrid
