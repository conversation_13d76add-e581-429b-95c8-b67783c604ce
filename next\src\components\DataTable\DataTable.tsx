import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  type TableOptions,
  useReactTable,
} from '@tanstack/react-table'
import clsx from 'clsx'
import { useEffect, useMemo, useState } from 'react'
import { FaCaretDown, FaCaretUp, FaSort } from 'react-icons/fa'
import DataTablePagination from '~/src/components/DataTable/Pagination/DataTablePagination'
import { getCommonPinningStyles } from '~/src/components/DataTable/Styles/DataTablePinningStyles'
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { DraggableRow } from '~/src/components/GoldIndex/DataTable/DragHandle'

interface DataTableProps<T extends object> {
  data: T[]
  columns: ColumnDef<T, any>[]
  extraConfig?: Partial<TableOptions<T>>
  isLoading?: boolean
  scrollOnDesktop?: boolean
  paginationEnabled?: boolean
  paginationClassName?: string
  onReorder?: (newData: T[]) => void
  disableDragDrop?: boolean
  categories?: string[]
  activeCategory?: string
  onCategoryChange?: (category: string) => void
}

const DataTable = <T extends object>({
  data,
  columns,
  extraConfig,
  scrollOnDesktop = false,
  paginationEnabled = false,
  isLoading,
  paginationClassName,
  onReorder,
  disableDragDrop = false,
  categories = [
    'ALL',
    'GOLD',
    'SILVER',
    'PGM',
    'BASE METALS',
    'BATTERY MATERIALS',
    'ENERGY',
    'RARE EARTH'
  ],
  activeCategory = 'GOLD',
  onCategoryChange = () => { },
}: DataTableProps<T>) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const rowsWithIds = useMemo(() => {
    if (!data || data.length === 0) return [];

    let result = data.map((item: any) => ({
      ...item,
      _rowId: item.commodity || item.id || item.Symbol || Math.random().toString(36),
    }));

    console.log("data", data);
    console.log("result", result);

    return result;
  }, [data]);

  const displayData = rowsWithIds;

  console.log("displayData", displayData);

  const table = useReactTable({
    data: displayData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    ...(extraConfig || {}),
  })

  const [selectedRowId, setSelectedRowId] = useState<string | null>(null)

  const handleRowClick = (rowId: string) => {
    setSelectedRowId(rowId === selectedRowId ? null : rowId)
  }

  useEffect(() => {
    if (table.getState().pagination.pageSize) {
      table.setPageSize(table.getState().pagination.pageSize)
    }
  }, [extraConfig])

  if (isLoading) {
    return <div>Loading...</div>
  }

  const handleCategoryChangeInternal = (category: string) => {
    if (onCategoryChange) {
      onCategoryChange(category);
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over?.id && onReorder) {
      const oldIndex = displayData.findIndex((item: any) => item.commodity === active.id);
      const newIndex = displayData.findIndex((item: any) => item.commodity === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const updatedData = [...displayData];
        const [movedItem] = updatedData.splice(oldIndex, 1);
        updatedData.splice(newIndex, 0, movedItem);
        onReorder(updatedData);
      }
    }
  };

  return (
    <div
      className={clsx(
        'w-full overflow-x-auto',
        scrollOnDesktop ? '' : 'lg:overflow-x-hidden',
      )}
    >
      <table className={`table-auto w-full`}>
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                const { column } = header
                const headerMeta: any = header.column.columnDef.meta
                const headerClassName = headerMeta?.classNameHeader

                return (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{ ...getCommonPinningStyles(column) }}
                    className={clsx(
                      'h-11 max-h-11 items-center justify-start self-stretch border-b border-slate-200 bg-neutral-100 px-2 text-left',
                      headerClassName,
                    )}
                  >
                    <div
                      className={clsx(
                        'flex cursor-pointer select-none items-center gap-2 text-xs font-bold leading-tight text-zinc-600',
                        header.column.getCanSort()
                          ? 'cursor-pointer select-none'
                          : '',
                        headerMeta?.classNameHeaderDiv,
                      )}
                      onClick={header.column.getToggleSortingHandler()}
                      onKeyDown={header.column.getToggleSortingHandler()}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}{' '}
                      {({
                        asc: <FaCaretUp />,
                        desc: <FaCaretDown />,
                      }[header.column.getIsSorted() as string] ??
                        extraConfig?.enableSorting) ? (
                        <FaSort />
                      ) : null}
                    </div>
                  </th>
                )
              })}
              {!disableDragDrop && <th className="w-8 px-2 bg-neutral-100 border-b border-slate-200" />}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) =>
            !disableDragDrop && isClient ? (
              <DraggableRow key={row.id} id={(row.original as any)._rowId}>
                {row.getVisibleCells().map((cell) => {
                  const cellMeta: any = cell.column.columnDef.meta
                  const cellClassName = cellMeta?.className

                  return (
                    <td
                      key={cell.id}
                      style={{
                        ...getCommonPinningStyles(cell.column),
                        width: cell.column.getSize(),
                      }}
                      className={clsx('p-0', cellClassName)}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </td>
                  )
                })}
              </DraggableRow>
            ) : (
              <tr
                key={row.id}
                onClick={() => handleRowClick(row.id)}
                className={clsx('bg-white', {
                  'bg-gray-100': row.id === selectedRowId,
                })}
              >
                {row.getVisibleCells().map((cell) => {
                  const cellMeta: any = cell.column.columnDef.meta
                  const cellClassName = cellMeta?.className

                  return (
                    <td
                      key={cell.id}
                      style={{
                        ...getCommonPinningStyles(cell.column),
                        width: cell.column.getSize(),
                      }}
                      className={clsx('p-0', cellClassName)}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </td>
                  )
                })}
              </tr>
            ),
          )}
        </tbody>
      </table>
      {paginationEnabled && (
        <div className={paginationClassName}>
          <DataTablePagination
            table={table}
            setPageSize={table.setPageSize}
            setPageIndex={table.setPageIndex}
          />
        </div>
      )}
    </div>
  )
}

export default DataTable
