vcl 4.0;

import std;
import str;
import dict;
import dynamic;

# Other VCL code
# Default backend definition. Points to Nginx, normally.
backend default {
    .host = "{{ .Values.varnish.backend.host }}";
    .port = "{{ .Values.varnish.backend.port }}";

    .connect_timeout = 60s;           # Timeout for establishing a connection to the backend
    .first_byte_timeout = 60s;        # Timeout for the first byte from the backend
    .between_bytes_timeout = 60s;     # Timeout between bytes received from the backend
    .max_connections = 200;           # Maximum number of connections to this backend
}

probe www_probe {
    .window = 6;
    .initial = 5;
    .threshold = 4;
    .timeout = 3s;
    .interval = 5s;
}

acl ipv4_only {
    "0.0.0.0"/0;
}

# subroutines
include "init.vcl";
include "recv.vcl";
include "synth.vcl";
include "deliver.vcl";

sub vcl_purge {
    # Only handle actual PURGE HTTP methods, everything else is discarded
    if (req.method != "PURGE") {
        # restart request
        set req.http.X-Purge = "Yes";
        return(restart);
    }
}

sub vcl_backend_response {
    # Grace in Varnish 4 means cached content will be held a little over it's TTL, the grace period, and the first client to request it
    # while the grace period exists triggers a backend request to update it in the background.  So everything is hitting
    # cache most of the time, but they're still triggering updates to the cached versions.
    # Check https://kly.no/posts/2015_09_25_Magic_Grace.html
    set beresp.grace = 1d;

    # Add the secret as a header so that we can later authorize ban requests.
    set beresp.http.X-Varnish-Secret = "{{ .Values.varnish.secret }}";

    # Snippet min cache 60 secs for no-cache : 100
    if (beresp.http.Cache-Control ~ "(no-store|no-cache)") {
        # we have to remove Set-Cookie since varnish auto ignore cache if `Set-Cookie` header is available
        unset beresp.http.Set-Cookie;
        unset beresp.http.Cache-Control;
        set beresp.ttl = 60s;
        set beresp.http.Cache-Control = "public, max-age=60";
    }
}
