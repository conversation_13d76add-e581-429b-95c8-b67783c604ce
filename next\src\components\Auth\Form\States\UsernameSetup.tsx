import { type User, onAuthStateChanged } from 'firebase/auth'
import type React from 'react'
import { useEffect, useState } from 'react'
import { CgSpinner } from 'react-icons/cg'
import { useDebounce } from 'use-debounce'
import Input from '~/src/components/Auth/Form/Elements/Input'
import SubmitButton from '~/src/components/Auth/Form/Elements/SubmitButton'
import { sanitizeUsername } from '~/src/features/auth/sanitize'
import Validator from '~/src/features/auth/validator'
import { getUserByEmail } from '~/src/services/discourse/api'
import { auth } from '~/src/services/firebase/config'
import { saveUserData } from '~/src/services/firebase/database'
import firebaseError from '~/src/services/firebase/errors'

/**
 * Props for the UsernameSetup component.
 */
interface UsernameSetupProps {
  onSuccess?: () => void
  backToLogin?: () => void
  onError?: (error: string) => void
}

const UsernameSetup: React.FC<UsernameSetupProps> = ({
  onSuccess,
  backToLogin,
  onError,
}: UsernameSetupProps) => {
  const debounceInterval = 300

  // Firebase user state
  const [user, setUser] = useState({} as User)

  // Forum user state
  const [isForumUser, setIsForumUser] = useState(false)

  // Field error state
  const [fieldError, setFieldError] = useState('')

  // Field states
  const [username, setUsername] = useState('')
  const [debouncedUsername] = useDebounce(username, debounceInterval)

  // Loading/sending states
  const [loading, setLoading] = useState(true)
  const [isSending, setIsSending] = useState(false)

  // State to indicate if the username has been saved
  const [usernameSaved, setUsernameSaved] = useState(false)

  /**
   * Fetch user data from Discourse
   *
   * @param user
   */
  const fetchUserDataFromDiscourse = async (user: User) => {
    // Set loading state while fetching user data from Discourse
    setLoading(true)

    // Check if the user is already a forum user
    setIsForumUser(false)

    // Fetch user data from Discourse
    const userData = await getUserByEmail(user.email)

    // If user data is not found, return
    if (!userData) {
      setLoading(false)
      return
    }

    // If the user has a username, set it in the state and save it
    if (userData.username) {
      setUsername(userData.username)

      try {
        const { lowercase } = sanitizeUsername(userData.username)
        await saveUserData(user, {
          // Save the username in lowercase
          username: lowercase,
          // Save the original username as the display username
          displayUsername: userData.username,
        })
      } catch (error) {
        console.error('Error trying to save the username:', error)

        // Handle Firebase error by showing an appropriate error message
        const errorMessage = firebaseError(
          error.code,
          'Failed to save the username. Please try again.',
        )

        // Show the error message to the user
        onError(errorMessage)
      }

      // Call the onSuccess callback if provided
      onSuccess()

      setIsForumUser(true)
    }

    setLoading(false)
  }

  /**
   * Wait for firebase auth to be initialized
   * Fetch user data from Discourse
   */
  useEffect(() => {
    // Wait for firebase auth to be initialized
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        setUser(user)
        fetchUserDataFromDiscourse(user)
      } else {
        setUser({} as User)
      }
    })

    // Clean up the subscription
    return () => unsubscribe()
  }, [])

  /**
   * Handle the username submit
   */
  const handleUsernameSubmit = async (e?: React.FormEvent<HTMLFormElement>) => {
    if (e) e.preventDefault()

    setIsSending(true)

    // Validate the field
    const isValid = await validateField(username)

    if (!isValid) {
      setIsSending(false)
      return
    }

    // Save the username
    try {
      const { original, lowercase } = sanitizeUsername(username)
      await saveUserData(user, {
        username: lowercase,
        displayUsername: original,
      })
    } catch (error) {
      console.error('Error trying to save the username:', error)

      // Handle Firebase error by showing an appropriate error message
      const errorMessage = firebaseError(
        error.code,
        'Failed to save the username. Please try again.',
      )

      // Show the error message to the user
      onError(errorMessage)
      setIsSending(false)
      return
    }

    setIsSending(false)
    setUsernameSaved(true)
    onSuccess()
  }

  /**
   * Validate a field based on the field name and value
   * This function is used to validate each field in the form
   * It sets the error message for the field if any
   * It also returns a boolean value to indicate if the field is valid
   * This function is called on field change
   *
   * @param value
   * @param setError
   */
  const validateField = async (value: string, setError = true) => {
    const errorMessage: string = !Validator.validateUsername(value)
      ? 'Username name cannot be empty, must be between 3 and 60 characters, and can only contain letters, numbers, dots and underscores.'
      : (await Validator.usernameExists(value, user.email))
        ? 'Username already exists. Please choose another username.'
        : ''

    // If setError is true, set the error message
    if (setError) {
      setFieldError(errorMessage)
    }

    return errorMessage === ''
  }

  /**
   * Handle the username field change
   *
   * @param e
   */
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { original } = sanitizeUsername(e.target.value)

    setUsername(original)
  }

  /**
   * Validate the username field on change
   */
  useEffect(() => {
    if (debouncedUsername) {
      validateField(debouncedUsername)
    }
  }, [debouncedUsername])

  if (usernameSaved) {
    return (
      <div className="sm:mx-auto sm:w-full sm:max-w-sm">
        <div className="flex flex-col items-center justify-center space-y-6">
          <h3 className="mt-4 text-xl font-bold text-ktc-blue">
            Username Saved
          </h3>

          <p className="text-center text-gray-600">
            Your username has been saved successfully!, please wait while you're
            being redirected.
          </p>

          <div className="my-4 flex w-full items-center justify-center text-center">
            <CgSpinner
              className="h-12 w-12 animate-spin text-ktc-blue"
              aria-hidden="true"
            />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="sm:mx-auto sm:w-full sm:max-w-sm">
      {/* UserName input */}
      <form className="mt-4 space-y-6" onSubmit={handleUsernameSubmit}>
        <div>
          <Input
            label="Username"
            id="username"
            name="username"
            type="text"
            autoComplete="username"
            required={true}
            value={username}
            disabled={isForumUser && username !== ''}
            onChange={handleChange}
            error={fieldError}
            hint={
              isForumUser
                ? 'You already have a username on the forum. You can change it in the profile later.'
                : ''
            }
          />
        </div>

        {/* Submit button */}
        <SubmitButton
          isSending={isSending}
          disabledCondition={!!fieldError || loading || isSending}
          buttonText="Save Username"
          loadingText="Saving..."
        />

        <div className="mt-4 text-center text-sm">
          <button
            type="button"
            onClick={backToLogin}
            className="text-blue-500 hover:text-blue-700"
          >
            Back to Login
          </button>
        </div>
      </form>
    </div>
  )
}

export default UsernameSetup
