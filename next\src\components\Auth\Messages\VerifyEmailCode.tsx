import { applyActionCode } from 'firebase/auth'
import type React from 'react'
import { useEffect, useState } from 'react'
import {
  BsEnvelope,
  BsEnvelopeCheck,
  BsEnvelopeExclamation,
} from 'react-icons/bs'
import { CgSpinner } from 'react-icons/cg'
import SubmitButton from '~/src/components/Auth/Form/Elements/SubmitButton'
import { AlertType } from '~/src/components/Auth/Messages/Alert'
import { auth } from '~/src/services/firebase/config'

/**
 * VerifyEmailCodeProps
 *
 * code: Verification code from email
 * onBack: Function to be called when the user clicks the back button
 * onAlert?: Function to be called when an alert is needed
 */
interface VerifyEmailCodeProps {
  code: string
  onBack: () => void
  onAlert?: (message: string, type?: AlertType) => void
}

/**
 * VerifyEmailCode Component
 *
 * @param {VerifyEmailCodeProps} props
 * @returns {React.ReactElement} React Component
 */
const VerifyEmailCode: React.FC<VerifyEmailCodeProps> = ({
  code,
  onBack,
  onAlert,
}: VerifyEmailCodeProps): React.ReactElement => {
  // State for the email verification process
  const [isVerifying, setIsVerifying] = useState(false)

  // State error state
  const [error, setError] = useState<boolean>(false)

  /**
   * Handle the email verification
   *
   * @returns {Promise<void>}
   */
  const handleVerifyEmail = async (): Promise<void> => {
    setIsVerifying(true)
    setError(false)
    try {
      // Apply the email verification code.
      await applyActionCode(auth, code)
    } catch {
      // Code is invalid or expired. Ask the user to verify their email address again.
      setError(true)

      if (onAlert) {
        onAlert(
          'Error verifying email: Invalid or expired action code.',
          AlertType.ERROR,
        )
      }
    } finally {
      setIsVerifying(false)
    }
  }

  /**
   * Handle the effect of the code changing
   */
  useEffect(() => {
    handleVerifyEmail()
  }, [code])

  return (
    <div className="sm:mx-auto sm:w-full sm:max-w-sm text-center">
      <div className="relative flex h-36 w-full items-center justify-center">
        <div className="absolute flex items-center justify-center">
          <div className="flex h-24 w-24 items-center justify-center rounded-full border-2 border-gray-300 bg-white">
            {isVerifying ? (
              <BsEnvelope className="text-6xl text-ktc-blue" />
            ) : error ? (
              <BsEnvelopeExclamation className="text-6xl text-ktc-blue" />
            ) : (
              <BsEnvelopeCheck className="text-6xl text-ktc-blue" />
            )}
          </div>
        </div>
      </div>

      {isVerifying ? (
        <>
          <div className="my-4 flex w-full items-center justify-center text-center">
            <CgSpinner
              className="h-12 w-12 animate-spin text-ktc-blue"
              aria-hidden="true"
            />
          </div>

          <p>Verifying your email address. Please wait...</p>
        </>
      ) : error ? (
        <>
          <h3 className="mb-4 text-lg font-bold">
            Something went wrong. Please try again.
          </h3>
          <p className="mt-4">
            The verification link is invalid or expired. Please login again to
            request a new verification email.
          </p>
        </>
      ) : (
        <>
          <h3 className="mb-4 text-lg font-bold">
            Your email has been verified!
          </h3>
          <p>Click the button below to continue.</p>
        </>
      )}

      <div className="mt-4 flex flex-col">
        <SubmitButton buttonText={'Continue'} onClick={onBack} />
      </div>
    </div>
  )
}

export default VerifyEmailCode
