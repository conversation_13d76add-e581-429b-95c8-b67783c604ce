import type { FC } from 'react'
import styles from './BreakingNewsJustIn.module.scss'

const JustInSVG: FC<any> = () => {
  return (
    <div className="h-full w-auto">
      <svg
        className="h-full w-auto"
        version="1.1"
        id="Layer_1"
        x="0px"
        y="0px"
        viewBox="0 0 838 297"
        xmlSpace="preserve"
      >
        <g>
          <polygon points="528,0 402,0 0,0 0,297 405,297 529.3,297 677.1,149.1 	" />
          <polygon
            className={styles.st0}
            points="674.8,0 594.2,0 746.7,152.5 602.2,297 682.8,297 827.3,152.5 	"
          />
        </g>
        <path
          className={styles.st1}
          d="M45.6,94.8c-0.1,2.1-0.1,4-0.1,5.9v3.7c-0.1,2.2-0.3,4.4-0.4,6.6c-0.1,2.2-0.1,4.4-0.1,6.6h15
	c0-1.9,0-3.8-0.1-5.7c-0.1-1.9-0.2-3.8-0.3-5.7V67.5c0.1-2,0.2-4,0.3-6s0.1-4.3,0.1-6.3H45.1c0,4.9,0.1,10.2,0.3,15.2
	c0.2,4.9,0.3,9.9,0.3,14.9v3.8c0,0.2,0,0.5,0,0.7C45.7,91.4,45.7,93,45.6,94.8z M61.3,84.8c-0.3,0.4-0.6,0.7-0.7,0.9
	c-0.2,0.3-0.2,0.6-0.2,0.9c0,0.5,0.2,1,0.5,1.3l28.7,29.6h16.1l-32-33.4L101,55.2H87.6L61.3,84.8z"
        />
        <path
          className={styles.st2}
          d="M108.1,70.9c-0.2-5.2-0.3-10.6-0.3-15.7h13.9c0,5.2-0.1,10.6-0.4,15.8c-0.2,5.2-0.4,10.4-0.4,15.7
	c0,5.2,0.1,10.3,0.4,15.4c0.2,5.1,0.4,10.3,0.4,15.4h-14.3c0-5.2,0.2-10.4,0.5-15.6s0.5-10.4,0.5-15.6
	C108.3,81.2,108.3,76.1,108.1,70.9z"
        />
        <path
          className={styles.st2}
          d="M147.1,75.3c0-3.7-0.2-7.5-0.6-11.2H144c-0.4,0.1-1.4,0.2-3,0.4l-5.2,0.6l-5.2,0.6c-1.6,0.2-2.6,0.3-3,0.4H127
	V55.2h51.7v10.7c-2.6,0-5.2-0.2-7.7-0.7s-5.1-0.7-7.7-0.7c-0.2,0-0.6,0-1.2,0c-0.6,0-1,0-1.2,0c-0.1,2.2-0.2,4.2-0.2,6.1
	s-0.1,3.7-0.1,5.4c0,1.8,0,3.6,0,5.4v6c0,5.1,0.2,10.1,0.5,15.1s0.5,10,0.5,15.1h-15.1c0-5.2,0.1-10.4,0.4-15.6
	c0.3-5.2,0.4-10.4,0.4-15.6V75.3H147.1z"
        />
        <path
          className={styles.st2}
          d="M182.4,70.5c1.2-4.2,3.2-7.4,6-9.7c2.8-2.2,6.5-3.8,11-4.5c5.6-0.8,11.3-1.2,16.9-1.2h2.2c1,0,2,0,2.9,0.1
	s1.8,0.1,2.5,0.1h6.1c0.5,0.1,1,0.3,1.5,0.5c0.5,0.3,0.7,0.9,0.6,1.4c-0.1,0.3-0.2,1-0.4,1.9s-0.4,2.2-0.6,3.2
	c-0.2,1.1-0.5,2.1-0.7,3s-0.3,1.5-0.4,1.9c-0.7-0.1-1.3-0.5-1.7-1.1s-1-1-1.7-1.3c-1.7-0.6-3.4-1-5.2-1.1c-1.7-0.1-3.5-0.2-5.3-0.2
	c-3.5-0.1-6.9,0.4-10.2,1.5c-2.8,1-5.1,2.3-6.8,4.3c-1.8,2.2-3.1,4.7-3.7,7.4c-0.8,3.4-1.2,7-1.2,10.5c-0.1,3.5,0.3,7,1,10.4
	c0.5,2.7,1.7,5.2,3.4,7.4c1.7,2,4,3.5,6.5,4.3c2.7,0.9,6.2,1.4,10.5,1.4c2.1,0,4.2-0.1,6.5-0.3c2.2-0.2,4.3-0.7,6.4-1.4
	c0.6-0.2,1.1-0.6,1.6-1s1.2-0.6,1.9-0.6v7.9c0,0.1-0.6,0.4-1.7,0.8c-2.4,0.7-4.8,1.1-7.2,1.2c-2.3,0.1-4.8,0.1-7.3,0.1
	c-4.4,0-8.9-0.3-13.3-0.9c-4.4-0.6-8.7-2.1-12.6-4.3c-2.1-1.1-3.8-2.7-5-4.6c-1.2-2-2.2-4.2-2.8-6.4c-0.6-2.4-1-4.8-1.1-7.2
	c-0.1-2.4-0.2-4.7-0.2-6.9C180.5,80.5,181.1,74.7,182.4,70.5z"
        />
        <path
          className={styles.st1}
          d="M245.9,112.6c3.6,1.5,7.5,2.4,11.4,2.7c4.2,0.3,8.2,0.5,12.1,0.5c2.6,0,5.3-0.1,8.1-0.2c2.7-0.2,5.4-0.5,8.1-1
	c2.6-0.5,5.1-1.2,7.5-2.2c2.3-0.9,4.4-2.2,6.2-3.8c1.8-1.7,3.3-3.7,4.2-5.9c1.1-2.7,1.7-5.5,1.6-8.4V75.5c0.1-3.1-0.7-6.2-2.3-8.9
	c-1.5-2.5-3.6-4.7-6.1-6.4c-2.6-1.7-5.4-3-8.4-3.8s-6.2-1.2-9.3-1.2h-20.1c-3.2,0-6.4,0.5-9.4,1.5c-2.9,0.9-5.6,2.4-8,4.3
	c-2.3,1.9-4.2,4.2-5.6,6.8c-1.4,2.8-2.1,5.9-2.1,9v17.9c0,4.9,1.1,8.8,3.4,11.6C239.6,109,242.6,111.2,245.9,112.6z M251.8,103.3
	c-1.7-2.1-2.8-4.5-3.3-7.1l0,0c-0.7-3.5-1-7-0.9-10.5c-0.1-3.4,0.3-6.8,1.1-10.1c0.6-2.6,1.9-5.1,3.7-7.1c1.8-1.9,4.2-3.4,6.7-4.2
	c3.3-1,6.8-1.4,10.3-1.4c3.6-0.1,7.1,0.4,10.6,1.4c2.6,0.8,5,2.2,6.9,4.2c1.8,2.1,3.1,4.5,3.7,7.2c0.8,3.4,1.2,6.8,1.1,10.2
	c0,3.6-0.3,7.2-1.1,10.7c-0.5,2.6-1.8,5-3.6,7c-1.9,1.9-4.3,3.2-6.9,3.8c-3.6,0.9-7.3,1.2-10.9,1.2c-4.4,0-8-0.4-10.8-1.3
	C255.8,106.7,253.5,105.3,251.8,103.3z"
        />
        <path
          className={styles.st1}
          d="M311.4,47.4c0.6,0.6,1.1,1.4,1.6,2.1l1.3,2.1h-2.1l-0.9-1.7c-0.4-0.9-1-1.8-1.8-2.5c-0.4-0.3-0.9-0.4-1.4-0.4
	h-1v4.5h-1.7V40.9h3.7c0.9,0,1.8,0.1,2.7,0.3c0.4,0.1,0.7,0.4,1,0.7c0.5,0.5,0.8,1.2,0.8,2c0,0.7-0.3,1.5-0.8,2
	c-0.6,0.6-1.4,0.9-2.2,1C310.8,46.9,311.1,47.1,311.4,47.4z M307.1,45.6h2.1l0,0c0.7,0.1,1.4-0.1,2.1-0.4c0.4-0.3,0.6-0.7,0.5-1.2
	c0-0.3-0.1-0.6-0.3-0.8c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.3-0.2-0.4-0.2c-0.6-0.2-1.1-0.2-1.7-0.2h-1.9v3.1
	C307.2,45.6,307.1,45.6,307.1,45.6z"
        />
        <path
          className={styles.st1}
          d="M309.8,37.3c-5.4,0-9.8,4.4-9.8,9.8s4.4,9.8,9.8,9.8s9.8-4.4,9.8-9.8C319.6,41.7,315.2,37.3,309.8,37.3z
	 M309.8,55.5c-4.6,0-8.4-3.8-8.4-8.4s3.8-8.4,8.4-8.4s8.4,3.8,8.4,8.4C318.2,51.8,314.5,55.5,309.8,55.5z"
        />
        <path
          className={styles.st2}
          d="M465.9,90.8h10.7l-5.4-16L465.9,90.8L465.9,90.8z"
        />
        <path
          className={styles.st2}
          d="M426.6,73h-7.1v26.7h7.1c4.9,0,8.9-0.9,8.9-6.3v-14C435.6,73.8,431.5,73,426.6,73z"
        />
        <path
          className={styles.st1}
          d="M326.5,55.2h182.3c1.4,0,2.7,0.5,3.6,1.5c1,0.9,1.5,2.2,1.5,3.6v50.6c0,2.8-2.3,5-5.1,5H326.5
	c-2.8,0-5.1-2.2-5.1-5V60.2C321.4,57.4,323.7,55.2,326.5,55.2z M375.4,102.4c1.4,0.2,2.8,0.2,4.2,0V68.5c-1.5-0.2-2.9-0.2-4.4,0
	l-11,20.5l-10.9-20.5c-1.5-0.2-3-0.2-4.6,0v33.8c1.4,0.2,2.8,0.2,4.2,0V75.5l9.7,18c1,0.2,2.1,0.2,3.1,0l9.6-17.9L375.4,102.4
	L375.4,102.4z M407.4,83.5c0.3,1.1,0.3,2.3,0,3.4h-14.5v12h15c0.3,1.1,0.3,2.3,0,3.5h-19.1V68.5h19.1c0.3,1.1,0.3,2.3,0,3.4h-15
	v11.5L407.4,83.5L407.4,83.5z M426.5,102.4c8.1,0,13.1-2.4,13.1-9.3V77.8c0-6.9-5-9.2-13.1-9.2h-11.2v33.8
	C415.3,102.4,426.5,102.4,426.5,102.4z M451.9,102.4c-1.4,0.2-2.8,0.2-4.2,0V68.5c1.4-0.2,2.8-0.2,4.2,0V102.4z M478.9,92.6l3.4,9.8
	l0,0c1.4,0.2,2.9,0.2,4.3,0l-12.2-33.9c-1.4-0.2-2.8-0.2-4.2,0L458,102.3c1.4,0.2,2.8,0.2,4.2,0l3.4-9.8L478.9,92.6L478.9,92.6z"
        />
        <path
          className={styles.st2}
          d="M186.5,153.1h13.1v41c0,5.4-0.5,9.5-1.4,12.4c-1.3,3.8-3.6,6.8-6.9,9.1c-3.3,2.3-7.7,3.4-13.2,3.4
	c-6.4,0-11.3-1.8-14.8-5.3c-3.5-3.6-5.2-8.8-5.2-15.8l12.4-1.4c0.1,3.7,0.7,6.3,1.6,7.9c1.4,2.3,3.6,3.5,6.5,3.5s5-0.8,6.2-2.5
	s1.8-5.1,1.8-10.4v-41.9H186.5z"
        />
        <path
          className={styles.st2}
          d="M215.6,153.1h13.1v35.1c0,5.6,0.2,9.2,0.5,10.8c0.6,2.7,1.9,4.8,4,6.4c2.1,1.6,5,2.4,8.7,2.4s6.5-0.8,8.4-2.3
	s3-3.4,3.4-5.6c0.4-2.2,0.6-5.9,0.6-11v-35.8h13.1v34c0,7.8-0.4,13.3-1.1,16.5s-2,5.9-3.9,8.1c-1.9,2.2-4.4,4-7.6,5.3
	c-3.2,1.3-7.4,1.9-12.5,1.9c-6.2,0-10.9-0.7-14.1-2.1c-3.2-1.4-5.7-3.3-7.6-5.6c-1.9-2.3-3.1-4.7-3.7-7.2c-0.9-3.7-1.3-9.2-1.3-16.4
	L215.6,153.1L215.6,153.1z"
        />
        <path
          className={styles.st2}
          d="M280,196.8l12.8-1.2c0.8,4.3,2.3,7.4,4.7,9.4c2.4,2,5.5,3,9.5,3c4.2,0,7.4-0.9,9.5-2.6c2.2-1.8,3.2-3.9,3.2-6.3
	c0-1.5-0.5-2.8-1.4-3.9c-0.9-1.1-2.5-2-4.7-2.8c-1.5-0.5-5-1.5-10.5-2.8c-7-1.7-12-3.9-14.8-6.4c-4-3.6-6-7.9-6-13
	c0-3.3,0.9-6.4,2.8-9.2c1.9-2.9,4.6-5.1,8.1-6.6s7.8-2.3,12.8-2.3c8.2,0,14.3,1.8,18.4,5.3c4.1,3.6,6.3,8.3,6.5,14.3l-13.1,0.6
	c-0.6-3.3-1.8-5.7-3.6-7.2c-1.8-1.5-4.6-2.2-8.3-2.2c-3.8,0-6.8,0.8-9,2.3c-1.4,1-2.1,2.3-2.1,4c0,1.5,0.7,2.8,2,3.9
	c1.7,1.4,5.7,2.8,12.1,4.3s11.1,3.1,14.1,4.7c3.1,1.6,5.5,3.8,7.2,6.6s2.6,6.2,2.6,10.3c0,3.7-1,7.2-3.1,10.4s-5,5.7-8.8,7.2
	c-3.8,1.6-8.5,2.3-14.1,2.3c-8.2,0-14.5-1.9-18.9-5.7C283.5,209.5,280.9,204,280,196.8z"
        />
        <path
          className={styles.st2}
          d="M360.6,217.9v-53.8h-19.3v-11H393v11h-19.2v53.8H360.6z"
        />
        <path className={styles.st2} d="M430.6,217.9v-64.7h13.1v64.7H430.6z" />
        <path
          className={styles.st2}
          d="M458.4,217.9v-64.7h12.8l26.6,43.2v-43.2H510v64.7h-13.2l-26.2-42.2v42.2H458.4z"
        />
        <line className={styles.st3} x1="158" y1="246" x2="515" y2="246" />
      </svg>
    </div>
  )
}

export default JustInSVG
