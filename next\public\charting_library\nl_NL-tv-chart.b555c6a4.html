<!DOCTYPE html><html lang="nl" dir="ltr"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=Edge"><script>window===window.parent&&(location.href="about:blank")</script><script defer="defer" crossorigin="anonymous" src="bundles/runtime.cc2522d6a97ad4920031.js"></script><script defer="defer" crossorigin="anonymous" src="bundles/vendors.0cfb111426f49f90fa86.js"></script><script defer="defer" crossorigin="anonymous" src="bundles/library.3afef79a8ae8c7a5618e.js"></script><link type="text/css" href="bundles/library.c9ed08366453322ce64f.css" rel="stylesheet"></head><body class="chart-page unselectable on-widget"><div class="loading-indicator" id="loading-indicator"></div><script>var JSServer={},__initialEnabledFeaturesets=["charting_library"]</script><script>!function(){window.urlParams=function(){function n(n){return decodeURIComponent(n.replace(t," ")).replace(/<\/?[^>]+(>|$)/g,"")}for(var e,t=/\+/g,r=/([^&=]+)=?([^&]*)/g,i=function(){var n=location.href,e=n.indexOf("#");if(0<=e)return n.substring(e+1);throw"Unexpected use of this page"}(),o={};e=r.exec(i);)o[n(e[1])]=n(e[2]);var s,a=window.parent[o.uid],l=["datafeed","customFormatters","brokerFactory","save_load_adapter"];for(s in a)-1===l.indexOf(s)&&(o[s]=JSON.stringify(a[s]));return o}(),window.locale=urlParams.locale,window.language=urlParams.locale,window.addCustomCSSFile=function(n){var e=document.createElement("link");e.setAttribute("type","text/css"),e.setAttribute("rel","stylesheet"),e.setAttribute("href",n),document.body.appendChild(e)},urlParams.customCSS&&window.addCustomCSSFile(urlParams.customCSS);var n={};if("string"==typeof urlParams.loading_screen)try{n=JSON.parse(urlParams.loading_screen)}catch(n){}var e=document.getElementById("loading-indicator");n.backgroundColor&&(e.style="background-color: "+n.backgroundColor),function(){"use strict";var n,e,t;i="\n/* Thanks to google guys for the original <paper-spinner> =)\n * https://github.com/PolymerElements/paper-spinner */\n.tv-spinner {\n  display: none;\n  position: absolute;\n  width: 1em;\n  height: 1em;\n  top: calc(50% - 0.5em);\n  left: calc(50% - 0.5em);\n  margin: 0 auto;\n  color: #37a6ef;\n  animation: tv-spinner__container-rotate 0.9s linear infinite;\n  will-change: transform;\n  /* The spinner does not have any contents that would have to be\n\t * flipped if the direction changes. Always use ltr so that the\n\t * style works out correctly in both cases. */\n  direction: ltr;\n}\n.tv-spinner--size_mini {\n  font-size: 16px;\n}\n.tv-spinner--size_medium {\n  font-size: 32px;\n}\n.tv-spinner--size_large {\n  font-size: 56px;\n}\n.tv-spinner--size_mini .tv-spinner__width_element:after {\n  border-width: 2px;\n}\n.tv-spinner--size_medium .tv-spinner__width_element:after {\n  border-width: 3px;\n}\n.tv-spinner--size_large .tv-spinner__width_element:after {\n  border-width: 4px;\n}\n.tv-spinner--shown {\n  display: block;\n}\n.tv-spinner__spinner-layer {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  white-space: nowrap;\n  color: currentColor;\n  transform: rotate(90deg);\n  /**\n\t\t * Patch the gap that appear between the two adjacent div.circle-clipper while the\n\t\t * spinner is rotating (appears on Chrome 50, Safari 9.1.1, and Edge).\n\t\t */\n}\n.tv-spinner__spinner-layer::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  border-width: 0.07em;\n  border-radius: 50%;\n  left: 45%;\n  width: 10%;\n  border-top-style: solid;\n}\n.tv-spinner__background {\n  display: inline-block;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n.tv-spinner__background::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  left: 0;\n  border-radius: 50%;\n  bottom: 0;\n  width: 100%;\n  border-color: rgba(135, 151, 165, 0.2);\n  border-style: solid;\n}\n.tv-spinner__circle-clipper {\n  display: inline-block;\n  position: relative;\n  width: 50%;\n  height: 100%;\n  overflow: hidden;\n}\n.tv-spinner__circle-clipper::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  border-radius: 50%;\n  bottom: 0;\n  width: 200%;\n  border-style: solid;\n  border-bottom-color: transparent;\n  animation-duration: 1.4s;\n  animation-timing-function: cubic-bezier(0.36, 0, 0.37, 0.99);\n  animation-iteration-count: 1;\n  will-change: transform;\n}\n.tv-spinner__circle-clipper--left::after {\n  left: 0;\n  border-right-color: transparent;\n  transform: rotate(0deg);\n  animation-name: tv-spinner__left-spin;\n}\n.tv-spinner__circle-clipper--right::after {\n  left: -100%;\n  border-left-color: transparent;\n  transform: rotate(-124deg);\n  animation-name: tv-spinner__right-spin;\n}\n@keyframes tv-spinner__container-rotate {\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@keyframes tv-spinner__left-spin {\n  0% {\n    transform: rotate(130deg);\n  }\n  to {\n    transform: rotate(0deg);\n  }\n}\n@keyframes tv-spinner__right-spin {\n  0% {\n    transform: rotate(-130deg);\n  }\n  to {\n    transform: rotate(-124deg);\n  }\n}\n",t=(n=void 0===n?{}:n).insertAt,"undefined"!=typeof document&&(e=document.head||document.getElementsByTagName("head")[0],(n=document.createElement("style")).type="text/css","top"===t&&e.firstChild?e.insertBefore(n,e.firstChild):e.appendChild(n),n.styleSheet?n.styleSheet.cssText=i:n.appendChild(document.createTextNode(i)));var s,a=new WeakMap;(i=s=s||{})[i.Element=1]="Element",i[i.Document=9]="Document";var r=function(){var n,e,t=(e=document.documentElement,(n=a?a.get(e):n)||((n=e.ownerDocument.createRange()).selectNodeContents(e),a&&a.set(e,n)),n.createContextualFragment('\n\t\t<div class="tv-spinner" role="progressbar">\n\t\t\t<div class="tv-spinner__spinner-layer">\n\t\t\t\t<div class="tv-spinner__background tv-spinner__width_element"></div>\n\t\t\t\t<div class="tv-spinner__circle-clipper tv-spinner__width_element tv-spinner__circle-clipper--left"></div>\x3c!--\n\t\t\t\t--\x3e<div class="tv-spinner__circle-clipper tv-spinner__width_element tv-spinner__circle-clipper--right"></div>\n\t\t\t</div>\n\t\t</div>\n\t'));if("firstElementChild"in t)r=t.firstElementChild;else for(var r=null,i=0;i<t.childNodes.length;i++){var o=t.childNodes[i];if(o.nodeType===s.Element){r=o;break}}return null!==r&&t.removeChild(r),r}(),i=(o.prototype.spin=function(n){return this._el.classList.add("tv-spinner--shown"),void 0===this._container&&(void 0!==(this._container=n)&&n.appendChild(this._el)),this._shown=!0,this},o.prototype.stop=function(n){return n&&void 0!==this._container&&this._container.removeChild(this._el),this._el.classList.remove("tv-spinner--shown"),this._shown=!1,this},o.prototype.setStyle=function(t){var r=this;return Object.keys(t).forEach(function(n){var e=t[n];void 0!==e&&r._el.style.setProperty(n,e)}),this},o.prototype.setSize=function(n){n=void 0!==n?"tv-spinner--size_"+n:"";return this._el.className="tv-spinner "+n+" "+(this._shown?"tv-spinner--shown":""),this},o.prototype.getEl=function(){return this._el},o.prototype.destroy=function(){this.stop(),delete this._el,delete this._container},o);function o(n){this._shown=!1,this._el=r.cloneNode(!0),this.setSize(n||"large")}window.Spinner=i}();var t=n.foregroundColor||"auto";(t=new Spinner("large").setStyle({color:t,zIndex:String(2e9)})).getEl().classList.add("spinner"),t.spin(e)}()</script><script>var _tv_languages=_tv_languages||{};_tv_languages.nl_NL={Save:"Opslaan",Show:"Toon",Delete:"Verwijderen",Cancel:"Annuleren",Price:"Prijs",Open:"Openen",Comment:"Reactie",Time:"Tijd",Search:"Zoeken",Date:"Datum",Description:"Beschrijving",Settings:"Instellingen",Width:"Breedte",Background:"Achtergrond",Border:"Rand",Apply:"Toepassen",Symbol:"Symbool",Exchange:"Beurs","Color Theme":"Kleuren Thema","Bar's Style":"Bar's stijl","Hollow Candles":"Lege candles",Line:"Lijn",Area:"Gebied",Grid:"Rooster",Add:"Toevoegen",Remove:"Verwijder",May:"Mei",Prices:"Prijzen",Error:"Fout","Invalid Symbol":"Onjuist symbool",More:"Meer",Quantity:"Hoeveelheid",Reverse:"Keer om",OK:"Oké",Compare:"Vergelijken",Close:"Slot",month_plural:"months",Hide:"Verbergen",m_interval_short:"m",h_interval_short:"h",D_interval_short:"D",W_interval_short:"W",M_interval_short:"M","Remove from favorites":"Verwijder van favorieten","Add to favorites":"Voeg toe aan favorieten","Time Interval":"Tijdinterval",Copy:"Kopiëren",Drawings:"Tekeningen",Indicators:"Indicatoren","Price Line":"Prijs lijn",UTC:"UTC+0","Sao Paulo":"São Paulo",Tehran:"Teheran",Tokyo:"Tokio",Athens:"Athene",Berlin:"Berlijn",London:"Londen",Moscow:"Moskou",Paris:"Parijs",Warsaw:"Warschau",Coordinates:"Coördinaten",Events:"Gebeurtenissen",Inputs:"Invoer",Properties:"Eigenschappen",Scales:"Schalen",Style:"Stijl","Timezone/Sessions":"Tijdzone/Sessies","Color bars based on previous close":"Kleur bars gebaseerd op voorgaand slot",Borders:"Randen",Wick:"Lont","Accumulation/Distribution_study":"Accumulatie/distributie","Accumulative Swing Index_study":"Accumulative Swing Index","Advance/Decline_study":"Advance/Decline","Arnaud Legoux Moving Average_study":"Arnaud Legoux Moving Average",Aroon_study:"Aroon",ASI_study:"ASI","Average Directional Index_study":"Average Directional Index","Average True Range_study":"Average True Range","Awesome Oscillator_study":"Awesome Oscillator","Balance of Power_study":"Balance of Power","Bollinger Bands %B_study":"Bollinger Bands %B","Bollinger Bands Width_study":"Bollinger Bands Width","Bollinger Bands_study":"Bollinger Bands","Chaikin Money Flow_study":"Chaikin Money Flow","Chaikin Oscillator_study":"Chaikin Oscillator","Chande Kroll Stop_study":"Chande Kroll Stop","Chande Momentum Oscillator_study":"Chande Momentum Oscillator","Chop Zone_study":"Chop Zone","Choppiness Index_study":"Choppiness Index","Commodity Channel Index_study":"Commodity Channel Index","Connors RSI_study":"Connors RSI","Coppock Curve_study":"Coppock Curve","Correlation Coefficient_study":"Correlation Coefficient",CRSI_study:"CRSI","Detrended Price Oscillator_study":"Detrended Price Oscillator","Directional Movement_study":"Directional Movement","Donchian Channels_study":"Donchian Channels","Double EMA_study":"Double EMA","Ease Of Movement_study":"Ease Of Movement","Elder's Force Index_study":"Elder's Force Index","EMA Cross_study":"EMA Cross",Envelopes_study:"Envelopes","Fisher Transform_study":"Fisher Transform","Fixed Range_study":"Fixed Range","Historical Volatility_study":"Historical Volatility","Hull Moving Average_study":"Hull Moving Average","Ichimoku Cloud_study":"Ichimoku Cloud","Keltner Channels_study":"Keltner Channels","Klinger Oscillator_study":"Klinger Oscillator","Know Sure Thing_study":"Know Sure Thing","Least Squares Moving Average_study":"Least Squares Moving Average","Linear Regression Curve_study":"Linear Regression Curve","MA Cross_study":"MA Cross","MA with EMA Cross_study":"MA with EMA Cross","MA/EMA Cross_study":"MA/EMA Cross",MACD_study:"MACD","Mass Index_study":"Mass Index","McGinley Dynamic_study":"McGinley Dynamic",Momentum_study:"Momentum","Money Flow_study":"Money Flow","Moving Average Channel_study":"Moving Average Channel","Moving Average Exponential_study":"Moving Average Exponential","Moving Average Weighted_study":"Moving Average Weighted","Moving Average_study":"Moving Average","Net Volume_study":"Net Volume","On Balance Volume_study":"On Balance Volume","Parabolic SAR_study":"Parabolic SAR","Pivot Points Standard_study":"Pivot Points Standard","Price Channel_study":"Price Channel","Price Oscillator_study":"Price Oscillator","Price Volume Trend_study":"Price Volume Trend","Rate Of Change_study":"Rate Of Change","Relative Strength Index_study":"Relative Strength Index","Relative Vigor Index_study":"Relative Vigor Index","Relative Volatility Index_study":"Relative Volatility Index","Session Volume_study":"Session Volume","Session Volume HD_study":"Session Volume HD","SMI Ergodic Indicator/Oscillator_study":"SMI Ergodic Indicator/Oscillator","Smoothed Moving Average_study":"Smoothed Moving Average","Stochastic RSI_study":"Stochastic RSI",Stochastic_study:"Stochastic","Triple EMA_study":"Triple EMA",TRIX_study:"TRIX","True Strength Indicator_study":"True Strength Indicator","Ultimate Oscillator_study":"Ultimate Oscillator","Visible Range_study":"Visible Range","Volume Oscillator_study":"Volume Oscillator",Volume_study:"Volume","Vortex Indicator_study":"Vortex Indicator",VWAP_study:"VWAP",VWMA_study:"VWMA","Williams %R_study":"Williams %R","Williams Alligator_study":"Williams Alligator","Williams Fractal_study":"Williams Fractal","Zig Zag_study":"Zig Zag","Plots Background_study":"Plots Background",SuperTrend_study:"SuperTrend","Average Price_study":"Average Price","Typical Price_study":"Typical Price","Median Price_study":"Median Price","Money Flow Index_study":"Money Flow Index","Moving Average Double_study":"Moving Average Double","Moving Average Triple_study":"Moving Average Triple","Moving Average Adaptive_study":"Moving Average Adaptive","Moving Average Hamming_study":"Moving Average Hamming","Moving Average Modified_study":"Moving Average Modified","Moving Average Multiple_study":"Moving Average Multiple","Linear Regression Slope_study":"Linear Regression Slope","Standard Error_study":"Standard Error","Standard Error Bands_study":"Standard Error Bands","Correlation - Log_study":"Correlation - Log","Standard Deviation_study":"Standard Deviation","Chaikin Volatility_study":"Chaikin Volatility","Volatility Close-to-Close_study":"Volatility Close-to-Close","Volatility Zero Trend Close-to-Close_study":"Volatility Zero Trend Close-to-Close","Volatility O-H-L-C_study":"Volatility O-H-L-C","Volatility Index_study":"Volatility Index","Trend Strength Index_study":"Trend Strength Index","Majority Rule_study":"Majority Rule",Length_input:"Length",Plot_input:"Plot",Zero_input:"Zero",Signal_input:"Signal",Long_input:"Long",Short_input:"Short",UpperLimit_input:"UpperLimit",LowerLimit_input:"LowerLimit",Offset_input:"Offset",length_input:"length",mult_input:"mult",short_input:"short",long_input:"long",Limit_input:"Limit",Move_input:"Move",Value_input:"Value",Method_input:"Method","Accumulation/Distribution_input":"Accumulation/Distribution",ADR_B_input:"ADR_B","Equality Line_input":"Equality Line","Window Size_input":"Window Size",Sigma_input:"Sigma","Aroon Up_input":"Aroon Up","Aroon Down_input":"Aroon Down",Upper_input:"Upper",Lower_input:"Lower",ADX_input:"ADX","ADX Smoothing_input":"ADX Smoothing","DI Length_input":"DI Length",Smoothing_input:"Smoothing",ATR_input:"ATR",Growing_input:"Growing",Falling_input:"Falling","Color 0_input":"Color 0","Color 1_input":"Color 1",Source_input:"Source",StdDev_input:"StdDev",Basis_input:"Basis",Median_input:"Median","Bollinger Bands %B_input":"Bollinger Bands %B",Overbought_input:"Overbought",Oversold_input:"Oversold","Bollinger Bands Width_input":"Bollinger Bands Width","RSI Length_input":"RSI Length","UpDown Length_input":"UpDown Length","ROC Length_input":"ROC Length",MF_input:"MF",resolution_input:"resolution","Fast Length_input":"Fast Length","Slow Length_input":"Slow Length","Chaikin Oscillator_input":"Chaikin Oscillator",P_input:"P",X_input:"X",Q_input:"Q",p_input:"p",x_input:"x",q_input:"q",Price_input:"Price","Chande MO_input":"Chande MO","Zero Line_input":"Zero Line","Color 2_input":"Color 2","Color 3_input":"Color 3","Color 4_input":"Color 4","Color 5_input":"Color 5","Color 6_input":"Color 6","Color 7_input":"Color 7","Color 8_input":"Color 8",CHOP_input:"CHOP","Upper Band_input":"Upper Band","Lower Band_input":"Lower Band",CCI_input:"CCI","WMA Length_input":"WMA Length","Long RoC Length_input":"Long RoC Length","Short RoC Length_input":"Short RoC Length",sym_input:"sym",Symbol_input:"Symbol",Correlation_input:"Correlation",Period_input:"Period",Centered_input:"Centered","Detrended Price Oscillator_input":"Detrended Price Oscillator",isCentered_input:"isCentered",DPO_input:"DPO","ADX smoothing_input":"ADX smoothing","+DI_input":"+DI","-DI_input":"-DI",DEMA_input:"DEMA",Divisor_input:"Divisor",EOM_input:"EOM","Elder's Force Index_input":"Elder's Force Index",Percent_input:"Percent",Exponential_input:"Exponential",Average_input:"Average","Upper Percentage_input":"Upper Percentage","Lower Percentage_input":"Lower Percentage",Fisher_input:"Fisher",Trigger_input:"Trigger",Level_input:"Level",HV_input:"HV","Hull MA_input":"Hull MA","Conversion Line Periods_input":"Conversion Line Periods","Base Line Periods_input":"Base Line Periods","Lagging Span 2 Periods_input":"Lagging Span 2 Periods",Displacement_input:"Displacement","Conversion Line_input":"Conversion Line","Base Line_input":"Base Line","Lagging Span_input":"Lagging Span","Lead 1_input":"Lead 1","Lead 2_input":"Lead 2","yay Color 0_input":"yay Color 0","yay Color 1_input":"yay Color 1",Multiplier_input:"Multiplier","Bands style_input":"Bands style",Middle_input:"Middle",useTrueRange_input:"useTrueRange",ROCLen1_input:"ROCLen1",ROCLen2_input:"ROCLen2",ROCLen3_input:"ROCLen3",ROCLen4_input:"ROCLen4",SMALen1_input:"SMALen1",SMALen2_input:"SMALen2",SMALen3_input:"SMALen3",SMALen4_input:"SMALen4",SigLen_input:"SigLen",KST_input:"KST",Sig_input:"Sig",roclen1_input:"roclen1",roclen2_input:"roclen2",roclen3_input:"roclen3",roclen4_input:"roclen4",smalen1_input:"smalen1",smalen2_input:"smalen2",smalen3_input:"smalen3",smalen4_input:"smalen4",siglen_input:"siglen","Upper Deviation_input":"Upper Deviation","Lower Deviation_input":"Lower Deviation","Use Upper Deviation_input":"Use Upper Deviation","Use Lower Deviation_input":"Use Lower Deviation",Count_input:"Count",Crosses_input:"Crosses",MOM_input:"MOM",MA_input:"MA","Length EMA_input":"Length EMA","Length MA_input":"Length MA","Fast length_input":"Fast length","Slow length_input":"Slow length","Signal smoothing_input":"Signal smoothing","Simple ma(oscillator)_input":"Simple ma(oscillator)","Simple ma(signal line)_input":"Simple ma(signal line)",Histogram_input:"Histogram",MACD_input:"MACD",fastLength_input:"fastLength",slowLength_input:"slowLength",signalLength_input:"signalLength",NV_input:"NV",OnBalanceVolume_input:"OnBalanceVolume",Start_input:"Start",Increment_input:"Increment","Max value_input":"Max value",ParabolicSAR_input:"ParabolicSAR",start_input:"start",increment_input:"increment",maximum_input:"maximum","Short length_input":"Short length","Long length_input":"Long length",OSC_input:"OSC",shortlen_input:"shortlen",longlen_input:"longlen",PVT_input:"PVT",ROC_input:"ROC",RSI_input:"RSI",RVGI_input:"RVGI",RVI_input:"RVI","Long period_input":"Long period","Short period_input":"Short period","Signal line period_input":"Signal line period",SMI_input:"SMI","SMI Ergodic Oscillator_input":"SMI Ergodic Oscillator",Indicator_input:"Indicator",Oscillator_input:"Oscillator",K_input:"K",D_input:"D",smoothK_input:"smoothK",smoothD_input:"smoothD","%K_input":"%K","%D_input":"%D","Stochastic Length_input":"Stochastic Length","RSI Source_input":"RSI Source",lengthRSI_input:"lengthRSI",lengthStoch_input:"lengthStoch",TRIX_input:"TRIX",TEMA_input:"TEMA","Long Length_input":"Long Length","Short Length_input":"Short Length","Signal Length_input":"Signal Length",Length1_input:"Length1",Length2_input:"Length2",Length3_input:"Length3",length7_input:"length7",length14_input:"length14",length28_input:"length28",UO_input:"UO",VWMA_input:"VWMA",len_input:"len","VI +_input":"VI +","VI -_input":"VI -","%R_input":"%R","Jaw Length_input":"Jaw Length","Teeth Length_input":"Teeth Length","Lips Length_input":"Lips Length",Jaw_input:"Jaw",Teeth_input:"Teeth",Lips_input:"Lips",jawLength_input:"jawLength",teethLength_input:"teethLength",lipsLength_input:"lipsLength","Down fractals_input":"Down fractals","Up fractals_input":"Up fractals",Periods_input:"Periods",Shapes_input:"Shapes","show MA_input":"show MA","MA Length_input":"MA Length","Color based on previous close_input":"Color based on previous close","Rows Layout_input":"Rows Layout","Row Size_input":"Row Size",Volume_input:"Volume","Value Area volume_input":"Value Area volume","Extend POC Right_input":"Extend POC Right","Value Area Volume_input":"Value Area Volume",Placement_input:"Placement",POC_input:"POC","Developing Poc_input":"Developing Poc","Up Volume_input":"Up Volume","Down Volume_input":"Down Volume","Value Area_input":"Value Area","Histogram Box_input":"Histogram Box","Value Area Up_input":"Value Area Up","Value Area Down_input":"Value Area Down","Number Of Rows_input":"Number Of Rows","Ticks Per Row_input":"Ticks Per Row","Up/Down_input":"Up/Down",Total_input:"Total","Deviation (%)_input":"Deviation (%)",Depth_input:"Depth","Extend to last bar_input":"Extend to last bar",Simple_input:"Simple",Weighted_input:"Weighted","Wilder's Smoothing_input":"Wilder's Smoothing","1st Period_input":"1st Period","2nd Period_input":"2nd Period","3rd Period_input":"3rd Period","4th Period_input":"4th Period","5th Period_input":"5th Period","6th Period_input":"6th Period","Rate of Change Lookback_input":"Rate of Change Lookback","Instrument 1_input":"Instrument 1","Instrument 2_input":"Instrument 2","Rolling Period_input":"Rolling Period","Standard Errors_input":"Standard Errors","Averaging Periods_input":"Averaging Periods","Days Per Year_input":"Days Per Year","Market Closed Percentage_input":"Market Closed Percentage","ATR Mult_input":"ATR Mult",VWAP_input:"VWAP","Anchor Period_input":"Anchor Period",Session_input:"Session",Week_input:"Week",Month_input:"Month",Year_input:"Year",Decade_input:"Decade",Century_input:"Century","Image URL":"Afbeelding URL","Save image":"Sla afbeelding op","Rename...":"Hernoem...","Load Chart Layout":"Laad grafiek lay-out",minutes_interval:"minutes",hours_interval:"hours",days_interval:"days",weeks_interval:"weeks",months_interval:"months","Toggle Auto Scale":"Schakel autoschaal","Toggle Log Scale":"Schakel log schaal","Toggle Percentage":"Schakel percentage",log_scale:"log",auto_scale:"auto",adj_adjustments:"adj","Date Range":"Datum reikwijdte","Hide Drawings Toolbar":"Verberg tekeningen werkbalk",Icon:"Icoon","Compare or Add Symbol":"Vergelijk of voeg een symbool toe","Fullscreen mode":"Volledig scherm modus","Open Interval Dialog":"Open interval dialoog",Seconds_interval_group_name:"Seconds",Minutes_interval_group_name:"Minutes",Hours_interval_group_name:"Hours",Days_interval_group_name:"Days",Weeks_interval_group_name:"Weeks",Months_interval_group_name:"Months",Ranges_interval_group_name:"Ranges",Crosshair:"Vizier","Chart Properties":"Grafiek eigenschappen","Make a Copy":"Maak een kopie","Rename Chart Layout":"Hernoem grafiek lay-out","Make a Copy...":"Maak een kopie...","Undo {0}":"Ongedaan maken {0}","Redo {0}":"Herhalen {0}","Add Alert":"Voeg alarm toe","Bring to Front":"Breng naar voren","Send to Back":"Stuur naar achteren","Bring Forward":"Breng naar voren","Send Backward":"Stuur naar achteren","Visual Order":"Visuele volgorde","Save As...":"Opslaan als...",Clone:"Kloon",Lock:"Op slot","Add Symbol_compare_or_add_symbol_dialog":"Add Symbol","Overlay the main chart":"Plaats over de primaire grafiek",Absolute:"Absoluut","Add to Watchlist {0}":"Voeg {0} toe aan volglijst",Eraser:"Gum","Trend Line":"Trendlijn","Horizontal Line":"Horizontale lijn","Vertical Line":"Verticale Lijn",Arrow:"Pijl",Ray:"Straal",Extended:"Uitgerekt","Parallel Channel":"Parallel kanaal",Pitchfork:"Hooivork","Schiff Pitchfork":"Schiff hooivork",Pitchfan:"Pitch waaier","Gann Square":"Gann vierkant","Gann Fan":"Gann waaier","Fib Retracement":"Fib teruggang","Trend-Based Fib Extension":"Trend gebaseerde Fib extensie","Fib Speed Resistance Fan":"Fib snelheid weerstandswaaier","Fib Time Zone":"Fib tijdszone","Circle Lines":"Cirkel lijnen","Fib Circles":"Fib cirkels","Fib Speed Resistance Arcs":"Fib snelheid weerstandsbogen",Rectangle:"Vierkant",Ellipse:"Ovaal",Triangle:"Driehoek",Polyline:"Polygoon",Arc:"Boog",Text_tool:"Text","Anchored Text":"Geankerde tekst",Balloon:"Ballon","Price Label":"Prijs label","Elliott Wave Subminuette":"Elliot subminuette golf","Elliott Wave Minor":"Elliot kleine golf","Elliott Wave Circle":"Elliott golfcyclus","Elliott Minor Retracement":"Elliot kleine teruggang","Elliott Major Retracement":"Elliott sterke teruggang",Brush:"Borstel",Forecast:"Voorspelling","Reset Chart":"Herstel grafiek",Undo:"Ongedaan maken",Redo:"Opnieuw","Time Zone":"Tijdszone","Change Symbol...":"Verander Symbool...","Change Interval...":"Verander interval...","Add To Watchlist":"Voeg toe aan volglijst","Insert Drawing Tool":"Voeg tekenhulpmiddel toe","Insert Indicator...":"Voeg indicator toe...","Compare or Add Symbol...":"Vergelijk of voeg een symbool toe...","Compare...":"Vergelijken..","Lock/Unlock":"Vergrendel/ontgrendel","Scale Price Chart Only":"Schaal alleen prijsgrafiek","Stay in Drawing Mode":"Blijf in teken modus","Stay In Drawing Mode":"Blijf in tekenmodus","Lock All Drawing Tools":"Vergrendel alle tekenhulpmiddelen","Hide All Drawing Tools":"Verberg alle tekenhulpmiddelen","Hide Marks On Bars":"Verberg markeringen op bars","Symbol Last Price Label":"Symbol Last Value Label","Source Code...":"Bron code...","Session Breaks":"Sessie onderbrekingen","Company Comparison":"Vergelijk ondernemingen","Zoom Out":"Uitzoomen","Zoom In":"Inzoomen",Defaults:"Standaard","XABCD Pattern":"XABC patroon","ABCD Pattern":"ABCD patroon","Arrow Mark Down":"Pijl teken beneden","Arrow Mark Left":"Pijl teken links","Arrow Mark Right":"Pijl teken rechts","Arrow Mark Up":"Pijl teken omhoog","Bars Pattern":"Bars patroon",Callout:"Aanroepen","Cyclic Lines":"Cyclische lijnen","Fib Channel":"Fib kanaal","Fib Spiral":"Fib spiraal","Fib Wedge":"Fib wig","Flag Mark":"Vlag markering","Flat Top/Bottom":"Vlakke top/bodem","Gann Box":"Gann box","Horizontal Ray":"Horizontale straal","Inside Pitchfork":"Interne hooivork",Note:"Notitie","Anchored Note":"Geankerde notitie","Price Range":"Prijs gebied",Projection:"Projectie","Regression Trend":"Regressie trend","Long Position":"Long positie","Short Position":"Short positie","Rotated Rectangle":"Gedraaide rechthoek","Modified Schiff Pitchfork":"Aangepaste Schiff hooivork","Trend Angle":"Trend hoek","Trend-Based Fib Time":"Trend gebaseerde Fib tijd","Triangle Pattern":"Driehoek patroon",Cross:"Kruis",Dot:"Punt",Color:"Kleur","Background Color":"Achtergrond Kleur","Left End":"Linker einde","Right End":"Rechter einde","Text color":"Tekstkleur","Profit Background Color":"Winst achtergrondkleur","Stop Background Color":"Stop achtergrondkleur","Border color":"Randkleur","Border Color":"Randkleur","Font Size":"Lettertype grootte","Marker Color":"Markeer kleur","Background color 1":"Achtergrond kleur 1","Background color 2":"Achtergrond kleur 2",Actual:"Daadwerkelijk","Lock Scale":"Vergrendel schaal",Percent_scale_menu:"Percent","Indexed to 100_scale_menu":"Indexed to 100",Logarithmic_scale_menu:"Logarithmic",Regular_scale_menu:"Regular","No Overlapping Labels_scale_menu":"No Overlapping Labels","Invert Scale_scale_menu":"Invert Scale",Watermark:"Watermerk","Top Margin":"Bovenste marge","Bottom Margin":"Onderste marge","Right Margin":"Rechter marge",bars_unit:"bars","Show Price":"Toon prijs","Color Bars Based on Previous Close":"Kleur bars gebaseerd op voorgaand slot","Show Price Range":"Toon prijs bereik","Show Bars Range":"Toon bar bereik","Show Date/Time Range":"Toon datum/tijd bereik","Show Distance":"Toon afstand","Show Angle":"Toon hoek","Always Show Stats":"Toon statistieken altijd",Text:"Tekst",Mirrored:"Gespiegeld",Flipped:"Omgedraaid","Extend Right":"Rek uit naar rechts","Extend Left":"Rek uit naar links","Price Levels":"Prijs levels","Left Labels":"Linker labels","Right Labels":"Rechter labels","Top Labels":"Bovenste labels","Bottom Labels":"Onderste labels",Fans:"Waaiers",Arcs:"Bogen","Extend left":"Rek uit naar links","Extend right":"Rek uit naar rechts","Label background":"Label achtergrond",Transparency:"Transparantie","#1 (price)_linetool point":"#1 (price)","#1 (price, bar)_linetool point":"#1 (price, bar)","#{0} (price, bar)_linetool point":"#{0} (price, bar)",Channel:"Kanaal",Median:"Mediaan","Extend Lines":"Rek lijnen uit",Original:"Origineel","Modified Schiff":"Aangepaste Schiff",Inside:"Binnen","Stop color":"Stop kleur","Target color":"Doel kleur:","Entry price":"Openingsprijs",Angle:"Hoek","#1 (bar)_linetool point":"#1 (bar)",Precision:"Precisie",High:"Hoog",Low:"Laag",Simple:"Eenvoudig","With Markers":"Met markeringen",Step:"Stap",Default:"Standaard","Override Min Tick":"Overschrijven minimale tick",Cross_chart_type:"Cross",Columns:"Kolommen",Circles:"Cirkels","Above Bar":"Boven bar","orders_Pyramiding: count orders":"orders","ticks_slippage ... ticks":"ticks",Offset:"Afstand","Main chart symbol_input":"Main chart symbol","Another symbol_input":"Another symbol","Text Color":"Tekstkleur","Trades on Chart":"Trades op de grafiek","Signal Labels":"Signaal labels","Show Labels":"Toon labels","Chart layout name":"Grafiek lay-out naam","Enter a new chart layout name":"Voer een nieuwe grafiek lay-out naam in","Please enter chart layout name":"Voer een grafiek lay-out naam in","Save New Chart Layout":"Sla nieuwe grafiek lay-out op","Copy Chart Layout":"Kopieer grafiek lay-out","{0} copy_ex: AAPL chart copy":"{0} copy","No symbols matched your criteria":"Geen symbool voldeed aan je criteria",Jan:"jan",Feb:"feb",Mar:"mrt",Apr:"apr",Jun:"jun",Jul:"jul",Aug:"aug",Sep:"sep",Oct:"Okt",Nov:"nov",Dec:"dec",d_dates:"d",h_dates:"h",m_dates:"m",s_dates:"s",Primary:"Primair",Minor_wave:"Minor",Minute_wave:"Minute",Subminuette:"Subminuten",SUCCESS:"Succes!",FAILURE:"Mislukt!",in_dates:"in","{0} P&L: {1}":"{0} winst & verlies: {1}",Open_line_tool_position:"Opened",Closed_line_tool_position:"Gesloten","Risk/Reward Ratio: {0}":"Risico/opbrengst ratio: {0}","Risk/Reward short":"Risico/opbrengst short","distance: {0}":"afstand: {0}",s_interval_short:"s",R_interval_short:"R",day_plural:"days",week_plural:"weeks",second_plural:"seconds",minute_plural:"minutes",hour_plural:"hours",range_plural:"ranges",ext_shorthand_for_extended_session:"ext",O_in_legend:"O",H_in_legend:"H",L_in_legend:"L",C_in_legend:"C",HL2_in_legend:"HL2",HLC3_in_legend:"HLC3",OHLC4_in_legend:"OHLC4","loading...":"laden...","Custom color...":"Aangepaste kleur...","Change Interval":"Verander interval","Not applicable":"Niet van toepassingen",Confirmation:"Bevestig","No indicators matched your criteria.":"Geen indicator voldeed aan je criteria",Su_day_of_week:"Su",Mo_day_of_week:"Mo",Tu_day_of_week:"Tu",We_day_of_week:"We",Th_day_of_week:"Th",Fr_day_of_week:"Fr",Sa_day_of_week:"Sa","in %s_time_range":"in %s","%s ago_time_range":"%s ago","%d minute_plural":"%d minutes","%d hour_plural":"%d hours","%d day_plural":"%d days","%d month_plural":"%d months","%d year_plural":"%d years",Light_colorThemeName:"Light",Dark_colorThemeName:"Dark",Normal:"Normaal","Add Symbol":"Voeg symbool toe","Add Custom Color_Color Picker":"Add Custom Color","Opacity_Color Picker":"Opacity","Add_Color Picker":"Add"}</script></body></html>