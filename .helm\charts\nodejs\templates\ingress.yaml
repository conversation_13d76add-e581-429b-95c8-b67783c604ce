apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ template "templ.fullname" . }}
  annotations:
    {{- if .Values.ingress.tls.cert_manager.enabled }}
    cert-manager.io/cluster-issuer: {{ .Values.ingress.tls.cert_manager.cluster_issuer }}
    certmanager.k8s.io/cluster-issuer: {{ .Values.ingress.tls.cert_manager.cluster_issuer }}
    {{- end }}
    {{- if .Values.ingress.redirect_www }}
    nginx.ingress.kubernetes.io/from-to-www-redirect: "true"
    {{- end }}
    # Allow any custom necessary annotations for different envs like AWS and GKE.
    # TODO: Consider using this generally instead of feature flags like cert_manager.enabled above for less magic
    {{- if .Values.ingress.annotations }}
{{ toYaml .Values.ingress.annotations | indent 4 }}
    {{- end }}
spec:
  ingressClassName: {{ .Values.ingress.class }}
  {{- if .Values.ingress.tls.enabled }}
  tls:
    - hosts:
        - {{ .Values.ingress.hostname | quote }}
    {{- if .Values.ingress.redirect_www }}
        - "www.{{ .Values.ingress.hostname }}"
    {{- end }}
    {{- if .Values.ingress.tls.cert_manager.enabled }}
      secretName: {{ template "templ.fullname" . }}-le
    {{- else }}
      secretName: {{ template "templ.fullname" . }}-custom-tls
    {{- end }}
    {{- end }}
  rules:
    - host: {{ .Values.ingress.hostname | quote }}
      http:
        paths:
          {{- $defaultServiceName := include "templ.fullname" . }}
          {{- $serviceName := default $defaultServiceName .Values.ingress.service.name }}
          {{- $servicePort := default 80 .Values.ingress.service.port }}
          - path: /.well-known/acme-challenge
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ $serviceName }}
                port:
                  number: {{ $servicePort }}
          - path: /chart-images/
            pathType: Prefix
            backend:
              service:
                name: nginx-chart-images-svc
                port:
                  number: 80
          - path: /news/rss
            pathType: Prefix
            backend:
              service:
                name: nginx-rss-svc
                port:
                  number: 80
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ $serviceName }}
                port:
                  number: {{ $servicePort }}
{{- if .Values.ingress.redirect_www }}
    - host: "www.{{ .Values.ingress.hostname }}"
      http:
        paths:
          - path: /chart-images/
            pathType: Prefix
            backend:
              service:
                name: nginx-chart-images-svc
                port:
                  number: 80
          - path: /news/rss
            pathType: Prefix
            backend:
              service:
                name: nginx-rss-svc
                port:
                  number: 80
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ $serviceName }}
                port:
                  number: {{ $servicePort }}
{{- end }}
