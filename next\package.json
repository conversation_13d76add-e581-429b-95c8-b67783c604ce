{"name": "next", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"build": "next build && yarn postbuild", "dev": "next dev -p 3080", "dev:public": "next dev -H 0.0.0.0 -p 3080", "dev:ssl": "next dev -p 3080 --experimental-https", "dev:ssl:public": "next dev -H 0.0.0.0 -p 3080 --experimental-https", "find:unused": "next-unused", "format": "biome format ./src --write & prettier ./src --write --log-level warn", "generate": "graphql-codegen --config codegen.ts && yarn format", "lint": "eslint --fix", "next-lint": "next lint", "postbuild": "next-sitemap", "postinstall": "node -e \"if (process.env.HUSKY === '0') { process.exit(0); } else { require('child_process').execSync('cd .. && husky next/.husky', { stdio: 'inherit' }); }\"", "start:nextcli": "next start -p 3080 --keepAliveTimeout 70000", "test": "jest", "type-check": "tsc --pretty", "watch:gen": "graphql-codegen --watch"}, "dependencies": {"@barchart/chart-lib": "^2.300.5", "@date-fns/tz": "^1.2.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@firebase/app": "^0.11.2", "@firebase/auth": "^1.9.1", "@firebase/database": "^1.0.13", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@google-cloud/logging-bunyan": "^5.1.0", "@google-cloud/storage": "^7.15.2", "@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@nivo/core": "^0.88.0", "@nivo/line": "^0.88.0", "@radix-ui/react-navigation-menu": "^1.2.5", "@reduxjs/toolkit": "^2.6.1", "@tanstack/react-query": "^5.68.0", "@tanstack/react-query-devtools": "^5.68.0", "@tanstack/react-table": "^8.21.2", "@types/react-redux": "^7.1.34", "@zxcvbn-ts/core": "^3.0.4", "@zxcvbn-ts/language-common": "^3.0.4", "@zxcvbn-ts/language-en": "^3.0.2", "axios": "^1.8.3", "bunyan": "^1.8.15", "chart.js": "^4.4.8", "classnames": "^2.5.1", "clsx": "^2.1.1", "currency-symbol-map": "^5.1.0", "currency.js": "^2.0.4", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "fast-fuzzy": "^1.12.0", "firebase": "^11.4.0", "firebase-admin": "^13.2.0", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "html-entities": "^2.5.2", "html-react-parser": "^5.2.2", "jotai": "^2.12.2", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mailgun-js": "^0.22.0", "next": "^14.2.14", "next-sitemap": "^4.2.3", "object-hash": "^3.0.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-advertising": "^4.2.15", "react-aria-components": "^1.7.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-error-boundary": "^4.1.2", "react-google-recaptcha-v3": "^1.10.1", "react-gravatar": "^2.6.3", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-medium-image-zoom": "^5.2.14", "react-redux": "^9.2.0", "react-share": "^5.2.2", "react-slick": "^0.30.3", "react-super-responsive-table": "^6.0.2", "react-tippy": "^1.4.0", "react-ts-tradingview-widgets": "^1.2.8", "reaptcha": "^1.12.1", "redux": "^5.0.1", "sass": "^1.86.0", "swr": "^2.3.3", "title-case": "^4.3.2", "use-debounce": "^10.0.4", "use-media": "1.5.0", "validator": "^13.12.0", "video.js": "^8.22.0", "videojs-offset": "^2.1.3", "xml2js": "^0.6.2", "yup": "^1.6.1", "zod": "^3.24.2"}, "devDependencies": {"@0no-co/graphqlsp": "^1.12.16", "@biomejs/biome": "1.9.4", "@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/client-preset": "^4.7.0", "@graphql-codegen/gql-tag-operations-preset": "^2.1.0", "@next/eslint-plugin-next": "^14.2.14", "@parcel/watcher": "^2.5.1", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-plugin-query": "^5.68.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/react-hooks": "^8.0.1", "@types/identity-obj-proxy": "^3", "@types/jest": "^29.5.14", "@types/js-cookie": "^3", "@types/jsonwebtoken": "^9.0.9", "@types/mailgun-js": "^0", "@types/node": "^22.13.10", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-gravatar": "^2.6.14", "@types/validator": "^13.12.2", "@types/video.js": "^7.3.58", "@types/xml2js": "^0", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "eslint": "^9.22.0", "eslint-config-next": "^14.2.14", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-next": "^0.0.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-tailwindcss": "^3.18.0", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.0", "next-unused": "^0.0.6", "postcss": "^8.5.3", "postcss-flexbugs-fixes": "^5.0.2", "postcss-preset-env": "^10.1.5", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "next-unused": {"alias": {}, "include": ["src/components", "src/components-news", "src/components-metals", "src/components-cryptos", "src/components-markets", "src/utils", "src/lib", "src/types", "src/styles"], "exclude": ["public", "styles", "node_modules", ".next", ".github", ".vscode", ".<PERSON><PERSON>", ".git"], "entrypoints": ["src/pages"]}, "packageManager": "yarn@4.7.0"}