.wrapper {
  position: relative;
  display: block;
}

.title {
  padding: 4px 0;
  background-color: #373737;
  color: white;
  display: grid;
  place-items: center;
  font-size: 18px;
}

.flexSpaceBetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  border: solid 1px #cccccc;
  border-top: 0;
  padding: 10px 1em;
}

.gridifier {
  display: grid;
  grid-template-columns: 123px 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  place-items: center;

  @media (min-width: 1270px) {
    grid-template-columns:
      123px 80px 69px 82px 82px 123px minmax(80px, 1fr)
      minmax(80px, 1fr);
  }
  & p {
    height: 52px;
    border-right: solid 1px #cccccc;
    border-bottom: solid 1px #cccccc;
    width: 100%;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
  }

  & p:first-of-type {
    border-left: solid 1px #cccccc;
  }

  & span {
    font-size: 12px;
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: solid 1px #cccccc;
    border-bottom: solid 1px #cccccc;
  }

  & span:first-of-type {
    display: flex;
    justify-content: flex-start;
    align-items: bottom;
    border-left: solid 1px #cccccc;

    & svg {
      margin-top: 2px;
      margin-right: 4px;
    }
  }
}

ul.listify {
  // width: fit-content;
  & li {
    width: 100%;
  }
  & li:nth-last-of-type(2n) {
    background-color: #f0f0f0;
  }
}

.bold {
  font-weight: 600;
  text-transform: uppercase;
  padding-left: 10px;
}

.changeRow {
  display: flex;
  width: 100%;
}

.change {
  border-bottom: solid 1px #cccccc;
  border-right: solid 1px #cccccc;
  width: 50%;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
}

.changeDown {
  color: red;
}

.changeUp {
  color: green;
}

.header {
  color: #fff;
  background-color: #373737;
  padding: 5px 10px;
  text-align: center;
}
.upper {
  text-transform: uppercase;
}
.up {
  color: #18a751;
}

.down {
  color: #a70202;
}

.market {
  text-align: center;
  padding: 5px 0;
}
