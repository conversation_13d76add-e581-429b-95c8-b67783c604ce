import { useCallback, useEffect, useRef, useState, useMemo } from 'react'
import { useGlobalIndexData } from '~/src/hooks/GlobalIndex/useGlobalIndexData'
import type { KGXCommodityData } from '~/src/types/DataTable/CommodityData'

/**
 * Get the KGX logic for the widget
 *
 * @param intervalTime - Time in milliseconds between updates
 */
function useKGXData(intervalTime = 5000) {
  // Set the index and pause state
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const [isTransitioning, setIsTransitioning] = useState(false)

  // Get the gold index data
  const data = useGlobalIndexData(false)
  const [commodities, setCommodities] = useState<KGXCommodityData[]>([])

  // Track the interval ID and timing
  const intervalIdRef = useRef<NodeJS.Timeout | null>(null)

  // Memoize special items to prevent recreation on every render
  const specialItems = useMemo(() => [
    // Rotation 5: KGX now covers the top blue-chip cryptos
    {
      getWidgetMessage: () => ({
        text: 'KGX now covers the top blue-chip cryptos',
        value: '...',
        isSpecialLink: true,
      }),
      commodity: 'crypto',
      totalChange: {
        change: '0.00',
        changeVal: 0,
        percentage: '0.00%',
        percentageVal: 0,
      },
      lastBid: {
        bid: '0',
        bidVal: 0,
        currency: 'USD',
        originalTime: new Date().toISOString(),
      },
      changeDueToUSD: {
        change: '0',
        changeVal: 0,
        percentage: '0',
        percentageVal: 0,
      },
      changeDueToTrade: {
        change: '0',
        changeVal: 0,
        percentage: '0',
        percentageVal: 0,
      },
      type: 'cryptoLink' as const,
    },
    // Rotation 6: Bitcoin
    {
      getWidgetMessage: function () {
        const isPositive = this.totalChange.changeVal > 0;
        const isZero = this.totalChange.changeVal === 0;
        const arrow = isZero ? '→' : isPositive ? '↑' : '↓';
        const color = isZero ? 'text-gray-600' : isPositive ? 'text-green-600' : 'text-red-600';

        return {
          text: 'Bitcoin',
          value: `${arrow} ${Math.abs(this.totalChange.changeVal)} (${this.totalChange.percentage})`,
          color,
          isPositive,
          isZero,
        };
      },
      commodity: 'Bitcoin',
      totalChange: {
        change: '0.00',
        changeVal: 0,
        percentage: '0.00%',
        percentageVal: 0,
      },
      lastBid: {
        bid: '0',
        bidVal: 0,
        currency: 'USD',
        originalTime: new Date().toISOString(),
      },
      changeDueToUSD: {
        change: '0',
        changeVal: 0,
        percentage: '0',
        percentageVal: 0,
      },
      changeDueToTrade: {
        change: '0',
        changeVal: 0,
        percentage: '0',
        percentageVal: 0,
      },
      type: 'cryptoCommodity' as const,
    },
    // Rotation 7: Ethereum
    {
      getWidgetMessage: function () {
        const isPositive = this.totalChange.changeVal > 0;
        const isZero = this.totalChange.changeVal === 0;
        const arrow = isZero ? '→' : isPositive ? '↑' : '↓';
        const color = isZero ? 'text-gray-600' : isPositive ? 'text-green-600' : 'text-red-600';

        return {
          text: 'Ethereum',
          value: `${arrow} ${Math.abs(this.totalChange.changeVal)} (${this.totalChange.percentage})`,
          color,
          isPositive,
          isZero,
        };
      },
      commodity: 'Ethereum',
      totalChange: {
        change: '0.00',
        changeVal: 0,
        percentage: '0.00%',
        percentageVal: 0,
      },
      lastBid: {
        bid: '0',
        bidVal: 0,
        currency: 'USD',
        originalTime: new Date().toISOString(),
      },
      changeDueToUSD: {
        change: '0',
        changeVal: 0,
        percentage: '0',
        percentageVal: 0,
      },
      changeDueToTrade: {
        change: '0',
        changeVal: 0,
        percentage: '0',
        percentageVal: 0,
      },
      type: 'cryptoCommodity' as const,
    },
    // Rotation 8: Learn more about the Kitco Global Index
    {
      getWidgetMessage: () => ({
        text: 'Learn more about the Kitco Global Index',
        value: 'Click here',
        isSpecialLink: true,
      }),
      commodity: 'learn-more',
      totalChange: {
        change: '0.00',
        changeVal: 0,
        percentage: '0.00%',
        percentageVal: 0,
      },
      lastBid: {
        bid: '0',
        bidVal: 0,
        currency: 'USD',
        originalTime: new Date().toISOString(),
      },
      changeDueToUSD: {
        change: '0',
        changeVal: 0,
        percentage: '0',
        percentageVal: 0,
      },
      changeDueToTrade: {
        change: '0',
        changeVal: 0,
        percentage: '0',
        percentageVal: 0,
      },
      type: 'learnMore' as const,
    },
  ], []); // Empty dependency array means this is created once on mount

  // Create commodities from data
  const createCommodities = useCallback((commodityData: typeof data) => {
    if (!commodityData || commodityData.length === 0) return [];

    const newCommodities = commodityData.map((commodity) => {
      const isPositive = commodity.totalChange.changeVal > 0
      const isZero = commodity.totalChange.changeVal === 0
      const arrow = isZero ? '→' : isPositive ? '↑' : '↓'
      const color = isZero
        ? 'text-gray-600'
        : isPositive
          ? 'text-green-600'
          : 'text-red-600'

      return {
        ...commodity,
        getWidgetMessage: () => ({
          text: `${commodity.commodity}`,
          value: `${arrow} ${Math.abs(commodity.totalChange.changeVal)} (${commodity.totalChange.percentage})`,
          color,
          isPositive,
          isZero,
        }),
        type: 'commodity' as const,
      }
    })

    // Combine regular commodities with special items
    return [...newCommodities, ...specialItems]
  }, [specialItems]) // Add specialItems as a dependency

  // Update commodities when data changes
  useEffect(() => {
    if (!data) return;

    const newCommodities = createCommodities(data);

    // Only update if the commodities have actually changed
    setCommodities(prevCommodities => {
      // If no previous commodities, just return the new ones
      if (prevCommodities.length === 0) return newCommodities;

      // If the number of items changed, we need to update
      if (prevCommodities.length !== newCommodities.length) {
        return newCommodities;
      }

      // Check if any commodities have actually changed
      const hasChanges = newCommodities.some((newItem, index) => {
        const oldItem = prevCommodities[index];
        return !oldItem ||
          newItem.commodity !== oldItem.commodity ||
          newItem.type !== oldItem.type;
      });

      if (hasChanges) {
        // Preserve the order of existing commodities if they match by name
        return newCommodities.sort((a, b) => {
          const aIndex = prevCommodities.findIndex(item => item.commodity === a.commodity);
          const bIndex = prevCommodities.findIndex(item => item.commodity === b.commodity);

          if (aIndex === -1) return 1;
          if (bIndex === -1) return -1;
          return aIndex - bIndex;
        });
      }

      // If nothing has changed, return the previous state to prevent re-render
      return prevCommodities;
    });
  }, [data, createCommodities]);

  // Use ref to track commodities without causing re-renders
  const commoditiesRef = useRef(commodities);

  // Update ref when commodities change
  useEffect(() => {
    commoditiesRef.current = commodities;
  }, [commodities]);

  // Handle the scrolling animation
  const handleScroll = useCallback(() => {
    if (isPaused || commoditiesRef.current.length <= 1) return;

    setIsTransitioning(true);

    // Calculate the next index using the ref
    setCurrentIndex(prevIndex => (prevIndex + 1) % commoditiesRef.current.length);

    // Reset transition after animation completes
    const timer = setTimeout(() => {
      setIsTransitioning(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [isPaused]); // No need to depend on commodities.length anymore

  // Set up the interval for auto-scrolling
  useEffect(() => {
    if (intervalIdRef.current) {
      clearInterval(intervalIdRef.current);
    }

    // Only set up interval if we have more than one item
    if (commoditiesRef.current.length > 1) {
      const intervalId = setInterval(() => {
        if (isPaused) return;
        handleScroll();
      }, intervalTime);

      // Store the interval ID
      intervalIdRef.current = intervalId;
    }

    // Clean up interval on unmount or when dependencies change
    return () => {
      if (intervalIdRef.current) {
        clearInterval(intervalIdRef.current);
      }
    };
  }, [isPaused, intervalTime, handleScroll]);

  // Pause scrolling when component is not visible
  const pauseScrolling = useCallback((shouldPause: boolean) => {
    setIsPaused(shouldPause);
  }, []);

  // Initial scroll setup - only run once on mount
  useEffect(() => {
    if (commodities.length > 1) {
      handleScroll();
    }
    // We only want this to run once on mount
  }, []); // Empty dependency array means this runs once on mount

  // Reorder commodities
  const reorderCommodities = useCallback((fromIndex: number, toIndex: number) => {
    setCommodities((prevCommodities) => {
      const newCommodities = [...prevCommodities];
      const [movedItem] = newCommodities.splice(fromIndex, 1);
      newCommodities.splice(toIndex, 0, movedItem);
      return newCommodities;
    });
  }, []);

  // Reset to default order
  const resetOrder = useCallback(() => {
    if (data && data.length > 0) {
      const defaultCommodities = createCommodities(data);
      setCommodities(defaultCommodities);
    }
  }, [data, createCommodities]);

  return {
    commodities,
    index: currentIndex,
    isLoading: data === undefined,
    isTransitioning,
    pause: isPaused,
    pauseScrolling,
    reorderCommodities,
    resetOrder,
  };
};

export default useKGXData;
