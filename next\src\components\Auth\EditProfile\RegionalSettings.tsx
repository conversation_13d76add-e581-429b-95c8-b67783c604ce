const RegionalSettings = () => {
  return (
    <>
      <h3 className="mx-0 mb-4 mt-0 border-0 border-solid text-xl font-bold leading-7">
        Language &amp; Time
      </h3>
      <div className="mb-4 border-0 border-solid">
        <label
          htmlFor="settings-language"
          className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
        >
          Select language
        </label>
        <select
          id="settings-language"
          name="countries"
          className="m-0 block w-full cursor-default appearance-none whitespace-pre rounded-lg border border-solid border-gray-300 bg-gray-50 bg-no-repeat p-2 text-base normal-case text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
        >
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            English (US)
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            Italiano
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            Français (France)
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            正體字
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            Español (España)
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            Deutsch
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            Português (Brasil)
          </option>
        </select>
      </div>
      <div className="mb-6 border-0 border-solid">
        <label
          htmlFor="settings-timezone"
          className="mb-2 block cursor-default border-0 border-solid text-sm font-medium leading-5 text-gray-900"
        >
          Time Zone
        </label>
        <select
          id="settings-timezone"
          name="countries"
          className="m-0 block w-full cursor-default appearance-none whitespace-pre rounded-lg border border-solid border-gray-300 bg-gray-50 bg-no-repeat p-2 text-base normal-case text-gray-900 focus:border-blue-600 focus:outline-offset-2 sm:text-sm sm:leading-5"
        >
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            GMT+0 Greenwich Mean Time (GMT)
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            GMT+1 Central European Time (CET)
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            GMT+2 Eastern European Time (EET)
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            GMT+3 Moscow Time (MSK)
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            GMT+5 Pakistan Standard Time (PKT)
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            GMT+8 China Standard Time (CST)
          </option>
          <option className="whitespace-nowrap border-0 border-solid text-sm leading-5">
            GMT+10 Eastern Australia Standard Time (AEST)
          </option>
        </select>
      </div>
      <div className="border-0 border-solid">
        <button className="m-0 cursor-pointer rounded-lg border-0 border-solid bg-blue-700 bg-none px-5 py-2 text-center text-sm font-medium normal-case leading-5 text-white">
          Save all
        </button>
      </div>
    </>
  )
}

export default RegionalSettings
