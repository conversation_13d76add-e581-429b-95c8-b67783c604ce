import Link from 'next/link'
import type { FC } from 'react'
import BlockHeader from '~/src/components/BlockHeader/BlockHeader'
import SkeletonTable from '~/src/components/SkeletonTable/SkeletonTable'
import type { LondonFixQuery } from '~/src/generated'
import dates from '~/src/utils/dates'
import styles from './LondonFixGridMobile.module.scss'

const LondonFixGridMobile: FC<{ data: LondonFixQuery }> = ({ data }) => {
  const dataAsArray = [
    data?.londonFixUSD,
    data?.londonFixEUR,
    data?.londonFixGBP,
  ]

  return (
    <div className={styles.wrapper}>
      <BlockHeader title={'London Fix Price'} />

      {/* dates section */}
      <h6 className={styles.date}>
        Quotes as of{' '}
        {dates.fmtUnix(data?.londonFixUSD?.results[0]?.timestamp - 1)}
      </h6>
      {/* column names */}
      <div className={styles.gridTitles}>
        <p>Currency</p>
        <p>
          Gold <br />
          <span className={styles.amPm}>AM</span>
        </p>
        <p>
          Silver <br />
          <span className={styles.amPm}>NOON</span>
        </p>
        <p>
          Platinum <br />
          <span className={styles.amPm}>AM</span>
        </p>
        <p>
          Palladium <br />
          <span className={styles.amPm}>AM</span>
        </p>
      </div>
      {/* column values */}
      <ul className={styles.listify}>
        {dataAsArray &&
          Object.entries(dataAsArray).map((x: any, idx: number) => (
            <li className={!(idx % 2) ? styles.altBg : styles.blank} key={idx}>
              <h6 className={styles.currencyName}>
                {x[1]?.currency ?? <SkeletonTable />}
              </h6>
              <div className={styles.dataValues}>
                {x[1]?.results[0]?.goldAM.toFixed(2) ?? <SkeletonTable />}
              </div>
              <div className={styles.dataValues}>
                {x[1]?.results[0]?.silver.toFixed(2) ?? <SkeletonTable />}
              </div>
              <div className={styles.dataValues}>
                {x[1]?.results[0]?.platinumAM.toFixed(2) ?? <SkeletonTable />}
              </div>
              <div className={styles.dataValues}>
                {x[1]?.results[0]?.palladiumAM.toFixed(2) ?? <SkeletonTable />}
              </div>
            </li>
          ))}
      </ul>
      <div className={styles.linkContainer}>
        <Link href="/price/fixes/london-fix">View London Fix</Link>
      </div>
    </div>
  )
}

export default LondonFixGridMobile
