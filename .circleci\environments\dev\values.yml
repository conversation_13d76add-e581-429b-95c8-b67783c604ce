nextjs:
  image:
    repository: "us.gcr.io/kitco-224816/kitco-frontend"
  nodeEnv: "production"
  extraEnvs:
  livenessProbe:
    enabled: true
    path: "/api/health"
  resources:
    requests:
      cpu: "400m"
      memory: "512Mi"
  ingress:
    enabled: true
    hostname: "frontend.dev.kitco.com"
    service:
      name: varnish
      port: 80
    annotations:
      # Allow ACME challenge requests on port 80
      nginx.ingress.kubernetes.io/configuration-snippet: |
        if ($server_port = 80) {
          set $allow_acme 1;
        }
      nginx.ingress.kubernetes.io/blacklist-source-range: *********/32, *********/32, *********/32
      # Redirect all HTTP requests to HTTPS
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
    tls:
      enabled: true
      secretName: "nextjs-frontend-tls-secret"

varnish:
  image:
    repository: us.gcr.io/kitco-224816/favish/varnish-frontend
    tag: 1.0.1
  backend:
    host: kitco-frontend-dev-nextjs
  replicas: 1
  resources:
    requests:
      cpu: 50m
      memory: 500M
  # Extra Varnish Configuration for templating
  extra:
    # Drupal Host
    drupalHost: "cms-drupal.dev.kitco.com"

    # Enable/Disable Basic Auth
    enableBasicAuth: true

    # Main Hostname
    hostname: "frontend.dev.kitco.com"

    # Enable/Disable Redirect to www
    redirect_www: false
    # Domain without www for comparison
    redirect_www_hostname: ""

    # Sitemap Bucket for Google Storage
    sitemapBucket: "kitco-cms-dev.storage.googleapis.com"

rss:
  resources:
    requests:
      cpu: 0.25
      memory: 0.5G
  ingressHost: news.dev.kitco.com
  ingressAnnotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    certmanager.k8s.io/cluster-issuer: "letsencrypt-prod"
  targetHost: cms-drupal.dev.kitco.com
  tls:
    enabled: true
    host: news.dev.kitco.com
