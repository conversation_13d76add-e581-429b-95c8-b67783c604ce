# Set a header to track a cache HITs and MISSes.
sub vcl_deliver {
    if (obj.hits > 0) {
        set resp.http.X-Cache = "HIT";
    }
    else {
        set resp.http.X-Cache = "MISS";
    }

    # Set a header to track TTLs
    # set resp.http.X-Cache-TTL = obj.ttl;

    # Add a header to indicate if this is a grace response
    # if (obj.ttl < 0s) {
    #     set resp.http.X-Cache-Grace = "GRACE";
    # }

    # Please note that obj.hits behaviour changed in 4.0, now it counts per objecthead, not per object
    # and obj.hits may not be reset in some cases where bans are in use. See bug 1492 for details.
    # So take hits with a grain of salt
    set resp.http.X-Cache-Hits = obj.hits;

    # To make live debugging a little easer, output the varnish hostname
    set resp.http.X-Served-By = server.hostname;

    # Remove all headers with unnecessary info disclosure
    unset resp.http.Link;
    unset resp.http.Server;
    unset resp.http.Via;
    unset resp.http.X-Varnish;
    unset resp.http.X-Generator;
    unset resp.http.X-Powered-By;
    unset resp.http.X-Varnish-Secret;

    # Very important to disable cache tags headers
    # These will be VERY large and it's unlikely your other
    # reverse proxies will accept them.
    unset resp.http.Cache-Tags;

    # Drupal cache headers
    unset resp.http.X-Drupal-Cache;
    unset resp.http.X-Drupal-Cache-Contexts;
    unset resp.http.X-Drupal-Cache-Tags;
    unset resp.http.X-Drupal-Dynamic-Cache;

    # Sensitive Headers
    unset resp.http.Surrogate-Key;
    unset resp.http.X-Served-By;
    unset resp.http.X-Cache-TTL;
    unset resp.http.Refresh;
    unset resp.http.X-Cache-Grace;

    # prevent MIME sniffing attacks 
    set resp.http.X-Content-Type-Options = "nosniff";

    return (deliver);
}
