import type { User } from 'firebase/auth'
import type React from 'react'
import Input from '~/src/components/Auth/Form/Elements/Input'
import SubmitButton from '~/src/components/Auth/Form/Elements/SubmitButton'
import { Alert, AlertType } from '~/src/components/Auth/Messages/Alert'
import { useLoginWithEmail } from '~/src/hooks/Auth/useLoginWithEmail'

/**
 * Props for the SignInWithEmail component.
 */
interface SignInWithEmailProps {
  onError: (error?: string) => void
  onForgotPassword?: (email: string) => void
  onLoginProcessStarted?: () => void
  onNeedUsername?: (user: User) => void
  onNeedVerification?: (user: User) => void
  onSuccess: () => void
  onUserNotFound: (email: string) => void
}

/**
 * Component for the email and password sign in form.
 */
const LoginWithEmail: React.FC<SignInWithEmailProps> = ({
  onError,
  onForgotPassword,
  onLoginProcessStarted,
  onNeedUsername,
  onNeedVerification,
  onSuccess,
  onUserNotFound,
}: SignInWithEmailProps): React.ReactElement => {
  const {
    email,
    password,
    emailError,
    passwordError,
    isLoading,
    showAlert,
    step,
    handleEmailChange,
    handlePasswordChange,
    handleLogin,
  } = useLoginWithEmail({
    onError,
    onLoginProcessStarted,
    onNeedUsername,
    onNeedVerification,
    onSuccess,
    onUserNotFound,
  })

  return (
    <>
      <form className="mt-4 space-y-6" onSubmit={handleLogin}>
        {showAlert && (
          <div>
            <Alert message="" type={AlertType.WARNING} title="Attention!">
              <h3 className="mb-2 font-bold">Attention!</h3>
              <p className="mb-2">
                This email address is already associated with your Kitco
                Publication access, either for regular operations or Press
                Releases.
              </p>
              <p>
                You can use the same credentials (email and password) to access
                the other Kitco Media Community services, like the Forum and the
                Engagement Platform.
              </p>
            </Alert>
          </div>
        )}
        <div>
          <Input
            label="Email address"
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required={true}
            value={email}
            error={emailError}
            onChange={handleEmailChange}
          />
        </div>
        {step === 2 && (
          <div>
            <Input
              label="Password"
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required={true}
              value={password}
              disabled={!email || !!emailError}
              error={passwordError}
              onChange={handlePasswordChange}
            >
              <div className="text-sm">
                <button
                  type="button"
                  role="link"
                  onClick={() => onForgotPassword(email)}
                  className="font-semibold text-ktc-blue hover:underline"
                >
                  Forgot password?
                </button>
              </div>
            </Input>
          </div>
        )}
        <div>
          <SubmitButton
            isSending={isLoading}
            disabledCondition={
              isLoading || !email || !!emailError || (step === 2 && !password)
            }
            buttonText={step === 1 ? 'Next' : 'Sign in'}
            loadingText="Processing..."
          />
        </div>
      </form>
    </>
  )
}

export default LoginWithEmail
