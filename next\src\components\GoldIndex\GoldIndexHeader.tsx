import DateTime from '~/src/components/DateTime/DateTime'

const GoldIndexHeader = () => {
  return (
    <div className="inline-flex w-full flex-col items-start justify-start gap-2">
      <h1 className="mt-1.5 font-['Lato'] text-3xl font-bold leading-none text-neutral-900">
        Kitco Global Index (KGX)
        <span className="sr-only">
          : Uncover the Real Value Behind Commodity & Crypto Prices
        </span>
      </h1>
      <h2 className="font-weight-normal font-['Lato'] text-xl leading-tight text-zinc-600">
        Separating Market Values from USD Influence
      </h2>
      <div className="flex gap-1 font-['Mulish'] text-xs font-normal leading-none text-zinc-600">
        <DateTime timeZone={process.env.NEXT_PUBLIC_TIMEZONE} /> NY Time
      </div>
      <div className="mt-5 w-full border-b border-slate-200" />
    </div>
  )
}

export default GoldIndexHeader
