  # Respond to incoming requests.
  sub vcl_recv {
    # Always respond happily to GC healthcheck
    # IT IS VERY IMPORTANT THAT THIS IS AT THE TOP OF EVERYTHING, HEALTHCHECK NEEDS A 200 NOT A 301
    if (req.http.User-Agent ~ "GoogleHC/1.0") {
      return (synth(200, "OK"));
    }

    # @see ticket https://favish.atlassian.net/browse/KTCR-1934 for cache poisioning issue
    #
    # Normalize the X-Forwarded-Host header to the value of the Host header
    # This prevents any manipulation of the X-Forwarded-Host header
    set req.http.x-forwarded-host = req.http.host;

    # Unset the X-Original-URL header to prevent its use in cache poisoning
    unset req.http.x-original-url;

    # Unset the X-Rewrite-URL header to prevent its use in cache poisoning
    unset req.http.x-rewrite-url;

    # Unset the X-Middleware-Prefetch header to prevent its use in cache poisoning
    unset req.http.x-middleware-prefetch;

    # Unset other potentially problematic X- headers
    unset req.http.x-forwarded-for;
    unset req.http.x-forwarded-proto;
    unset req.http.x-forwarded-port;
    unset req.http.x-forwarded-server;
    unset req.http.x-real-ip;
    unset req.http.x-client-ip;
    unset req.http.x-request-id;
    #
    # END of cache poisioning

    # This allows to bypass cache for testing purposes using url?FavishNoCache
    if (req.url ~ "\?FavishNoCache") {
      return(pass);
    }

    # Snippet API calls - Do not cache and bypass basic auth : 10
    if (req.url ~ "^/api/") {
      return(pass);
    }

    # Determine the protocol (http or https)
    if (req.http.X-Forwarded-Proto) {
      set req.http.X-Proto = req.http.X-Forwarded-Proto;
    } else {
      if (std.port(local.ip) == 443) {
        set req.http.X-Proto = "https";
      } else {
        set req.http.X-Proto = "http";
      }
    }

    # Redirect from bare domain to www
    {{ if .Values.varnish.extra.redirect_www }}
    if (!str.startswith(req.http.host, "www.") && (req.http.host == "{{ .Values.varnish.extra.redirect_www_hostname }}")) {
      set req.http.host = "www." + req.http.host;
      return (synth(830, "Permanent Redirect to WWW"));
    }
    {{ end }}

    # Basic Authentication
    {{ if .Values.varnish.extra.enableBasicAuth }}
    if (! req.http.Authorization ~ "Basic ZmF2aXNoOnJlcGxhdGZvcm0h") {
      return(synth(401, "Authentication required"));
    }
    unset req.http.Authorization;
    {{ end }}

    # Block potential scrapers or known malicious bots
    if (req.http.User-Agent ~ "(yandexbot|baiduspider|sosospider|HTTrack|Pcore-HTTP)") {
      return (synth(403, "Forbidden"));
    }

    # 403 Requests to paths with previous malicious/invalid activity
    if (
      req.url ~ "^/images/track/track.gif" ||
      req.url ~ "cdnjs.cloudflare.com" ||
      req.url ~ "d.agkn.com" ||
      req.url ~ "ssp.lkqd.net" ||
      req.url ~ "^/wp-login" ||
      req.url ~ "bfmio.com"
      ) {
      return (synth(403, "Forbidden"));
    }

    # Implementing websocket support (https://www.varnish-cache.org/docs/4.0/users-guide/vcl-example-websockets.html)
    if (req.http.Upgrade ~ "(?i)websocket") {
      return (pipe);
    }

    # if they are looking for rss feeds we need to push thre traffic to a differend ingress/service by changeing the http host header to drupal.
    if(req.url == "/news/rss/kitconewsfeedextended.xml") {
      set req.url = "/rss/kitconewsfeedextended.xml";
    }

    if (
      req.url == "/rss/kitcocommentariesfeed.xml" ||
      req.url == "/rss/kitcofeed.xml" ||
      req.url == "/rss/kitconewsfeed.xml" ||
      req.url == "/rss/kitconewsfeedcrypto.xml" ||
      req.url == "/rss/kitconewsfeedextended.xml" ||
      req.url == "/rss/kitcomining.xml"
    ) {
      # Change the host header to point to the different backend
      set req.http.host = "{{ .Values.varnish.extra.drupalHost }}";
      # set req.http.host = "kitco-cms-prod.storage.googleapis.com";
      set req.backend_hint = www_dir.backend("{{ .Values.varnish.extra.drupalHost }}");

      return (pass);
    } else {
      # THIS MAKE THE WHOLE SITE WORK DO NOT DELETE IT
      # our "host" origin uses http host headers to determine how to route the incoming requests. by default lets assume everyting is for nextjs.
      set req.http.host = "{{ .Values.varnish.extra.hostname }}";
    }

    # handle redirects
    include "redirects.vcl";

    # purging
    if (req.method == "PURGE") {
      # Check for the presence of a secret key.
      if (!req.http.X-Varnish-Purge) {
        return (synth(405, "Permission denied."));
      }

      return (purge);
    }

    # cache purges
    if (req.method == "BAN") {
      # Check for the presence of a secret key.
      if (!req.http.X-Varnish-Purge) {
        return (synth(405, "Permission denied."));
      }

      # Only allow BAN requests from internal php pod
      # unfortunately, we cannot issue ACL due to it just used to match with IP
      # so has do implement this this way `req.http.host !~ "^(varnish|host2.com|host3.com)$"`.
      # By logging internal k8s host system.
      # I found that host is alwasy `varnish` when a request is fired from inside k8s cluster
      std.log("BAN for: " + req.http.host + req.url);

      if (req.http.Cache-Tags) {
        # Ban based on the secret and cache tags.
        ban("obj.http.X-Varnish-Secret == " + req.http.X-Varnish-Purge + " && obj.http.Cache-Tags ~ " + req.http.Cache-Tags);
        # Throw a synthetic page so the request won't go to the backend.
        return (synth(200, "Ban added."));
      }
      else {
        return (synth(403, "Cache-Tags header missing."));
      }

      # Parse the ban URL from the request
      if (req.http.url) {
        ban("req.url == " + req.http.url);
      } else {
        return (synth(400, "Bad Request"));
      }

      return (synth(200, "Ban added"));
    }

    # Only cache GET and HEAD requests (pass through POST requests).
    if (req.method != "GET" && req.method != "HEAD") {
      return (pass);
    }

    include "cache_bypasses.vcl";

    # Normalize the header, remove the port (in case you're testing this on various TCP ports)
    set req.http.Host = regsub(req.http.Host, ":[0-9]+", "");

    # Remove the proxy header (see https://httpoxy.org/#mitigate-varnish)
    unset req.http.proxy;

    # Unset basic-auth header.  Allows caching from behind a basic-auth proxy.
    unset req.http.Authorization;

    # Normalize the query arguments
    set req.url = std.querysort(req.url);

    # Add an X-Forwarded-For header with the client IP address.
    if (req.restarts == 0) {
      if (req.http.X-Forwarded-For) {
        set req.http.X-Forwarded-For = req.http.X-Forwarded-For + ", " + client.ip;
      }
      else {
        set req.http.X-Forwarded-For = client.ip;
      }
    }

    # Pass through any administrative or AJAX-related paths.
    if (req.url ~ "^/status\.php$" ||
      req.url ~ "^/update\.php$" ||
      req.url ~ "^/admin$" ||
      req.url ~ "^/admin/.*$" ||
      req.url ~ "^/flag/.*$" ||
      req.url ~ "^.*/ajax/.*$" ||
      req.url ~ "^.*/ahah/.*$" ||
      req.url ~ "/statistics\.php$" ||
      # Ads.txt sometimes has a Drupal managed redirect rule
      req.url ~ "/ads.txt" ||
      # Revive is an OSS ad server we include alongside Drupal in some cases
      req.url ~ "^/revive/.*$" ||
      # Sitemap ignores
      req.url ~ "sitemap" ||
      req.url ~ "^/arrowchat/.*$" ||
      req.url ~ "^/system/files/.*$" ||
      req.url ~ "^/googlenews\.xml$" ) {
        return (pass);
    }

    # Remove the nocache query parameter and treat it as a regular request
    # We have experienced flooding with this parameter and unix timestamps
    if (req.url ~ "(\?|&)(nocache)=") {
      set req.url = regsuball(req.url, "&(nocache)=([A-z0-9_\-\.%25]+)", "");
      set req.url = regsuball(req.url, "\?(nocache)=([A-z0-9_\-\.%25]+)", "?");
      set req.url = regsub(req.url, "\?&", "?");
      set req.url = regsub(req.url, "\?$", "");
    }

    # Strip hash, server doesn't need it.
    if (req.url ~ "\#") {
      set req.url = regsub(req.url, "\#.*$", "");
    }

    # Strip a trailing ? if it exists
    if (req.url ~ "\?$") {
      set req.url = regsub(req.url, "\?$", "");
    }

    # without this one, request cannot be cached
    # see https://varnish-cache.org/docs/6.0/users-guide/increasing-your-hitrate.html#cookies:~:text=Also%2C%20if%20the%20client%20sends%20a%20Cookie%20header%2C%20Varnish%20will%20bypass%20the%20cache%20and%20go%20directly%20to%20the%20backend.
    unset req.http.Cookie;

    # Removing cookies for static content so Varnish caches these files.
    # STATIC_FORMATS set in run.sh and replaced when container starts, used again in vcl_backend_response below
    # if (req.url ~ "(?i)\.({{ .Values.varnish.staticFormats }})(\?.*)?$") {
    #   unset req.http.Cookie;
    #   return (hash);
    # }

    # Remove all cookies that Drupal doesn't need to know about. We explicitly
    # list the ones that Drupal does need, the SESS and NO_CACHE. If, after
    # running this code we find that either of these two cookies remains, we
    # will pass as the page cannot be cached.

    # if (req.http.Cookie) {
    #   # 1. Append a semi-colon to the front of the cookie string.
    #   # 2. Remove all spaces that appear after semi-colons.
    #   # 3. Match the cookies we want to keep, adding the space we removed
    #   #  previously back. (\1) is first matching group in the regsuball.
    #   # 4. Remove all other cookies, identifying them by the fact that they have
    #   #  no space after the preceding semi-colon.
    #   # 5. Remove all spaces and semi-colons from the beginning and end of the
    #   #  cookie string.
    #   set req.http.Cookie = ";" + req.http.Cookie;
    #   set req.http.Cookie = regsuball(req.http.Cookie, "; +", ";");
    #   set req.http.Cookie = regsuball(req.http.Cookie, ";(SESS[a-z0-9]+|SSESS[a-z0-9]+|NO_CACHE|XDEBUG_SESSION)=", "; \1=");
    #   set req.http.Cookie = regsuball(req.http.Cookie, ";[^ ][^;]*", "");
    #   set req.http.Cookie = regsuball(req.http.Cookie, "^[; ]+|[; ]+$", "");
    #   if (req.http.Cookie == "") {
    #     # If there are no remaining cookies, remove the cookie header. If there
    #     # aren't any cookie headers, Varnish's default behavior will be to cache
    #     # the page.
    #     unset req.http.Cookie;
    #   }
    #   else {
    #     # If there is any cookies left (a session or NO_CACHE cookie), do not
    #     # cache the page. Pass it on to backend directly.
    #     return (pass);
    #   }
    # }
  }