import type React from 'react'

/**
 * AccountCreatedProps is a React Component properties that passed to React
 * Account Created Message Component
 *
 * onClose: Function that will be called when the close button is clicked
 */
interface AccountCreatedProps {
  onClose: () => void
}

/**
 * AccountCreated is a React Component that will be displayed when user
 * successfully created an account
 *
 * @param {AccountCreatedProps} props - React Component properties
 * @returns {React.ReactElement} React Component
 */
const AccountCreated: React.FC<AccountCreatedProps> = ({
  onClose,
}: AccountCreatedProps): React.ReactElement => {
  return (
    <div className="my-6 text-center">
      <h2 className="mb-4 text-xl font-bold text-ktc-blue">Congratulations!</h2>
      <h3 className="mb-4 text-lg font-bold">
        You have created your Kitco account
      </h3>
      <p>Please check your email to verify your account.</p>
      <button
        type="button"
        onClick={onClose}
        className="mt-4 font-semibold text-ktc-blue hover:text-ktc-black"
      >
        Back to Login
      </button>
    </div>
  )
}

export default AccountCreated
