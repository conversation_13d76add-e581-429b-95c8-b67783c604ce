import type { NextRequest } from 'next/server'

export const config = {
  runtime: 'edge',
}

const BaseURL = 'https://proxy.kitco.com'

export default async function handler(req: NextRequest) {
  const url = new URL(req.url)
  const symbols = url.pathname.replace(
    '/api/getKGX/',
    '/getValue?ver=2.0&tz=Africa/Algiers&df=2&tf=2&type=json&kgx=yes&symbol=',
  )

  try {
    const response = await fetch(`${BaseURL}${symbols}`)
    if (!response.ok) {
      throw new Error(`Failed to fetch data from ${BaseURL}${symbols}`)
    }
    const data = await response.json()

    return new Response(JSON.stringify(data), {
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Fetch error:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}
