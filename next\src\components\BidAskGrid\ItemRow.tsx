import dayjs from 'dayjs'
import Link from 'next/link'
import styles from '~/src/components/BidAskGrid/BidAskGrid.module.scss'
import Icon from '~/src/components/Icon/Icon'
import useWeight from '~/src/hooks/Weight/useWeight'
import WeightType from '~/src/types/WeightSelect/WeightType'
import { renderFn } from '~/src/utils/SangHai/priceConversion'
import cs from '~/src/utils/cs'
import { Timezones } from '~/src/utils/dates'
import isNegative from '~/src/utils/isNegative'

function styleUpOrDown(xue: number) {
  if (isNegative(xue)) {
    return styles.changeDown
  }
  return styles.changeUp
}

const ItemRow = ({
  ask,
  bid,
  change,
  changePercentage,
  high,
  low,
  name,
  timestamp,
  isBaseMetals,
}) => {
  const weight = useWeight(
    isBaseMetals ? WeightType.BaseMetals : WeightType.PreciousMetals,
  )

  const metalTitle = (name: string) => {
    const urlPath = isBaseMetals
      ? `/price/base-metals/${name.toLowerCase()}`
      : `/price/precious-metals/${name.toLowerCase()}`

    if (name.toUpperCase() === 'RHODIUM')
      return <span className="!border-0 pl-[10px]">{name}</span>

    return (
      <Link href={urlPath} className={styles.bold}>
        {name}
      </Link>
    )
  }

  return (
    <li>
      <div className={styles.gridifier}>
        <span className={styles.bold}>
          <Icon icon="barchart" size="12px" />
          {metalTitle(name)}
        </span>
        <span>
          {!timestamp
            ? '-'
            : dayjs.tz(timestamp, Timezones.NY).format('MMM D, YYYY')}
        </span>
        <span>
          {!timestamp ? '-' : dayjs.tz(timestamp, Timezones.NY).format('HH:mm')}
        </span>
        <span>
          {!bid
            ? '-'
            : renderFn(weight, bid, {
                symbol: '',
                precision: isBaseMetals ? 4 : 2,
              })}
        </span>
        <span>
          {!ask
            ? '-'
            : renderFn(weight, ask, {
                symbol: '',
                precision: isBaseMetals ? 4 : 2,
              })}
        </span>
        <div className={styles.changeRow}>
          <div className={cs([styles.change])}>
            <div className={styleUpOrDown(change)}>
              {!change
                ? '-'
                : renderFn(weight, change, { symbol: '', precision: 3 })}
            </div>
          </div>
          <div className={styles.change}>
            <div className={styleUpOrDown(change)}>
              {!changePercentage
                ? '-'
                : `${renderFn(weight, changePercentage, { symbol: '', precision: 2 })}%`}
            </div>
          </div>
        </div>
        <span>
          {!low
            ? '-'
            : renderFn(weight, low, {
                symbol: '',
                precision: isBaseMetals ? 4 : 2,
              })}
        </span>
        <span>
          {!high
            ? '-'
            : renderFn(weight, high, {
                symbol: '',
                precision: isBaseMetals ? 4 : 2,
              })}
        </span>
      </div>
    </li>
  )
}

export default ItemRow
