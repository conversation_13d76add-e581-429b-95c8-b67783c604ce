import Link from 'next/link'
import type React from 'react'
import styles from './BlockHeader.module.scss'

interface BlockHeaderProps {
  title: string
  href?: string
}

const TitleComponent: React.FC<{ title: string }> = ({ title }) => (
  <h2 className="text-base font-semibold capitalize">{title}</h2>
)

const LinkComponent: React.FC<{ href: string; children: React.ReactNode }> = ({
  href,
  children,
}) => (
  <Link href={href}>
    <h2 className="text-base font-semibold capitalize text-white">
      {children}
    </h2>
  </Link>
)

const BlockHeader: React.FC<BlockHeaderProps> = ({ title, href }) => {
  const renderContent = () => {
    if (href) {
      return <LinkComponent href={href}>{title}</LinkComponent>
    }
    return <TitleComponent title={title} />
  }

  return <header className={styles.header}>{renderContent()}</header>
}

export default BlockHeader
