import clsx from 'clsx'
import Link from 'next/link'
import type { FC } from 'react'
import { BsPlusSquareFill } from 'react-icons/bs'

const ArticleMoreButton: FC<{
  title: string
  href: string
}> = ({ title, href }) => {
  return (
    <Link
      href={href}
      className={clsx(
        'flex items-center gap-2',
        'group hover:bg-[#1D61AE] active:bg-[#144985]',
      )}
    >
      <div
        className={clsx(
          'flex items-center justify-center gap-2',
          'w-full border border-[#E2E8F0] py-2',
        )}
      >
        <BsPlusSquareFill className="mt-[.10rem] text-[#1D61AE] group-hover:bg-[#1D61AE] group-hover:text-[#ffffff] group-active:bg-[#1D61AE] group-active:text-[#ffffff]" />
        <span className="font-bold text-[#1D61AE] underline group-hover:!text-[#ffffff] group-active:!text-[#ffffff]">
          {title}
        </span>
      </div>
    </Link>
  )
}

export default ArticleMoreButton

export const ArticleMoreButtonNewsPages: FC<{
  href: string
  label: string
}> = ({ href, label }) => {
  return (
    <Link
      className={clsx(
        'mt-5 w-full px-4 py-[5px]',
        'inline-block',
        'border border-kitco-black',
        'tracking-widertext-sm text-center text-[12px] font-bold uppercase text-kitco-black',
        'transition hover:opacity-80',
      )}
      href={href}
    >
      {label}
    </Link>
  )
}
