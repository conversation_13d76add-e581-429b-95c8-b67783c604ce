
# Check for redirections
# order is important here we need to detect original http Host and redirect traffic for
# most news.kitco.com traffic to kitco.com/news due to fastly weirdness this has to be done
# in another snippet sub_error
if (std.tolower(req.http.host) == "news.kitco.com" && req.url !~ "/rss/") {
    set req.http.X-Redirect-URL = req.proto + "://" +  "{{ .Values.varnish.extra.hostname }}/news";
    return (synth(619, "Redirect"));
}

if (req.url ~ {"(?i)^/forex\?tvwidgetsymbol=(?:FX_IDC%3A)?([A-Z]+)\z"}) {
    # Extract the variable part of the symbol
    set req.http.X-Forex-Symbol = regsub(req.url, "(?i)^/forex\?tvwidgetsymbol=(?:FX_IDC%3A)?([A-Z]+)\z", "\1");
    # Set a custom error to handle the redirect
    return (synth(620, "Redirect"));
}

# do redirect for all the market furtures pages to /markets
if (req.url ~ "^/markets/futures") {
    set req.http.X-Query-String = regsub(req.url, "^[^?]*\\?(.*)$", "\1");  
    set req.http.X-Value = "/markets";
    return (synth(681, "Redirect"));
}

# Condition for all old paths lookup
set req.http.X-Path = regsub(req.url, "^([^?]*).*$", "\1");

if (dict.lookup(std.tolower(req.http.X-Path))) {
    set req.http.X-Dict-Entry = dict.lookup(std.tolower(req.http.X-Path));

    # if found, then extract response code from
    set req.http.X-Code = regsub(req.http.X-Dict-Entry, "^([^:]+):(.*)$", "\1");
    set req.http.X-Value = regsub(req.http.X-Dict-Entry, "^([^:]+):(.*)$", "\2");
    set req.http.X-Query-String = regsub(req.url, "^[^?]*\\?(.*)$", "\1");
    unset req.http.X-Path;

    return (synth(std.integer(req.http.X-Code, 0), "Redirect"));
}

# Check if the request URL starts with /images/
if (req.url ~ "^/LFgif/" || req.url ~ "^/kitconetcharts/" || req.url ~ "^/images/live/gold.gif(.*)") {
    # Issue a synthetic response with 404 status
    return (synth(404, "Not Found"));
}
