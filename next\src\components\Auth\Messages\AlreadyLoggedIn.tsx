import type React from 'react'
import { useEffect } from 'react'
import { CgSpinner } from 'react-icons/cg'
import SubmitButton from '~/src/components/Auth/Form/Elements/SubmitButton'

/**
 * AlreadyLoggedInProps is a React Component properties that passed to React
 * Account Created Message Component
 *
 * onClose: Function that will be called when the close button is clicked
 */
interface AlreadyLoggedInProps {
  buttonText?: string
  disableRedirectButton?: boolean
  loadingText?: string
  processing?: boolean
  onClose: () => void
  onLogout: () => void
}

/**
 * AlreadyLoggedIn is a React Component that will be displayed when user
 * successfully created an account
 *
 * @param {AlreadyLoggedInProps} props - React Component properties
 * @returns {React.ReactElement} React Component
 */
const AlreadyLoggedIn: React.FC<AlreadyLoggedInProps> = ({
  buttonText = 'Continue',
  disableRedirectButton = false,
  loadingText = 'Processing...',
  processing = false,
  onClose,
  onLogout,
}: AlreadyLoggedInProps): React.ReactElement => {
  useEffect(() => {
    onClose()
  }, [])

  return (
    <div className="sm:mx-auto sm:w-full sm:max-w-sm">
      <div className="my-6 flex flex-col items-center justify-center gap-4 text-center">
        <h2 className="mb-4 text-xl font-bold text-ktc-blue">
          Please wait while you're being redirected
        </h2>

        <div className="my-4 flex w-full items-center justify-center text-center">
          <CgSpinner
            className="h-12 w-12 animate-spin text-ktc-blue"
            aria-hidden="true"
          />
        </div>

        <p className="text-lg">
          If you are not redirected in a few seconds, click the button below.
        </p>

        <SubmitButton
          isSending={processing}
          disabledCondition={disableRedirectButton}
          buttonText={buttonText}
          loadingText={loadingText}
          onClick={onClose}
        />

        <button
          type="button"
          onClick={onLogout}
          className="font-semibold text-ktc-blue hover:text-ktc-black"
        >
          Log out
        </button>
      </div>
    </div>
  )
}

export default AlreadyLoggedIn
