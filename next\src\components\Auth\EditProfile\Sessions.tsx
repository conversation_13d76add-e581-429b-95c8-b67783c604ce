const Sessions = () => {
  return (
    <>
      <div className="flow-root border-0 border-solid">
        <h3 className="m-0 border-0 border-solid text-xl font-bold leading-7">
          Sessions
        </h3>
        <ul className="m-0 border-0 border-solid p-0">
          <li className="py-4 text-left">
            <div className="flex items-center border-0 border-solid">
              <div className="flex-shrink-0 border-0 border-solid">
                <svg
                  className="block h-6 w-6 border-0 border-solid align-middle"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    className="border-0 border-solid"
                  />
                </svg>
              </div>
              <div className="ml-4 mr-0 min-w-0 flex-1 border-0 border-solid">
                <p className="m-0 truncate border-0 border-solid text-base font-semibold text-gray-900">
                  California 123.123.123.123
                </p>
                <p className="m-0 truncate border-0 border-solid text-sm font-normal leading-5 text-gray-500">
                  Chrome on macOS
                </p>
              </div>
              <div className="ml-4 mr-0 inline-flex items-center border-0 border-solid">
                <a
                  href="#"
                  className="mb-3 mr-3 cursor-pointer rounded-lg border border-solid border-gray-300 px-3 py-2 text-center text-sm font-medium leading-5 text-gray-900"
                >
                  Revoke
                </a>
              </div>
            </div>
          </li>
          <li className="border-x-0 border-b-0 border-t border-solid border-gray-200 pb-6 pt-4 text-left">
            <div className="flex items-center border-0 border-solid">
              <div className="flex-shrink-0 border-0 border-solid">
                <svg
                  className="block h-6 w-6 border-0 border-solid align-middle"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                    className="border-0 border-solid"
                  />
                </svg>
              </div>
              <div className="ml-4 mr-0 min-w-0 flex-1 border-0 border-solid">
                <p className="m-0 truncate border-0 border-solid text-base font-semibold text-gray-900">
                  Rome 24.456.355.98
                </p>
                <p className="m-0 truncate border-0 border-solid text-sm font-normal leading-5 text-gray-500">
                  Safari on iPhone
                </p>
              </div>
              <div className="ml-4 mr-0 inline-flex items-center border-0 border-solid">
                <a
                  href="#"
                  className="mb-3 mr-3 cursor-pointer rounded-lg border border-solid border-gray-300 px-3 py-2 text-center text-sm font-medium leading-5 text-gray-900"
                >
                  Revoke
                </a>
              </div>
            </div>
          </li>
        </ul>
        <div className="border-0 border-solid">
          <button className="m-0 cursor-pointer rounded-lg border-0 border-solid bg-blue-700 bg-none px-5 py-2 text-center text-sm font-medium normal-case leading-5 text-white">
            See more
          </button>
        </div>
      </div>
    </>
  )
}

export default Sessions
