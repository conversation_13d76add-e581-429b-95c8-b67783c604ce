import type React from 'react'
import Gravatar<PERSON>ithRef from '~/src/components/GravatarWithRef/GravatarWithRef'

interface AvatarProps {
  email: string
  displayName: string
}

const Avatar: React.FC<AvatarProps> = ({ email, displayName }: AvatarProps) => {
  return (
    <>
      <div className="items-center border-0 border-solid sm:flex xl:block">
        <GravatarWithRef
          email={email}
          className="mb-4 block h-24 w-24 max-w-full rounded-lg border-0 border-solid align-middle sm:mb-0 xl:mb-4"
          size={250}
        />
        <div className="border-0 border-solid sm:mx-0 xl:mx-0">
          <h3 className="mx-0 mb-1 mt-0 border-0 border-solid text-2xl font-bold leading-8 text-gray-900">
            {displayName}
          </h3>
          <div className="mb-4 border-0 border-solid text-base font-normal text-gray-500">
            User
          </div>
          <a
            href="#"
            className="inline-flex cursor-pointer items-center rounded-lg border-0 border-solid bg-blue-700 px-3 py-2 text-center text-sm font-medium leading-5 text-white"
          >
            <svg
              className="-ml-1 mr-2 block h-4 w-4 border-0 border-solid align-middle"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M5.5 13a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 13H11V9.413l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13H5.5z"
                className="border-0 border-solid"
              />
              <path
                d="M9 13h2v5a1 1 0 11-2 0v-5z"
                className="border-0 border-solid"
              />
            </svg>
            Change picture
          </a>
        </div>
      </div>
    </>
  )
}

export default Avatar
