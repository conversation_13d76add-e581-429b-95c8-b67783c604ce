import type { User } from 'firebase/auth'
import Link from 'next/link'
import type React from 'react'
import <PERSON>gin<PERSON>egalFooter from '~/src/components/Auth/Form/LoginLegalFooter'
import LoginWithEmail from '~/src/components/Auth/Form/States/LoginWithEmail'
import LoginWithThirdProviders from '~/src/components/Auth/Form/States/LoginWithThirdProviders'

/**
 * LoginMethodsProps
 *
 * @property {Function} onLoginError
 * @property {Function} [onSuccess]
 * @property {Function} onRegister
 * @property {Function} [onForgotPassword]
 * @property {Function} [onNeedVerification]
 */
interface LoginMethodsProps {
  onForgotPassword?: (email?: string) => void
  onLoginError: (error?: string) => void
  onLoginProcessStarted?: (method: string) => void
  onNeedUsername?: () => void
  onNeedVerification?: (user: User) => void
  onRegister: (email?: string) => void
  onSuccess?: (shouldReload: boolean) => void
  showRegister?: boolean
}

/**
 * LoginMethods
 *
 * @param {LoginMethodsProps} props
 * @returns {React.ReactElement} - Rendered component
 */
const LoginMethods: React.FC<LoginMethodsProps> = ({
  onForgotPassword,
  onLoginError,
  onLoginProcessStarted,
  onNeedUsername,
  onNeedVerification,
  onRegister,
  onSuccess,
  showRegister = true,
}: LoginMethodsProps): React.ReactElement => {
  const enableThirdProviders = false

  return (
    <>
      <div className="sm:mx-auto sm:w-full sm:max-w-sm">
        <LoginWithEmail
          onError={onLoginError}
          onForgotPassword={onForgotPassword}
          onLoginProcessStarted={() => onLoginProcessStarted('email')}
          onNeedUsername={onNeedUsername}
          onNeedVerification={onNeedVerification}
          onSuccess={() => onSuccess(true)}
          onUserNotFound={onRegister}
        />
        {showRegister && (
          <p className="mt-10 text-center text-sm text-gray-500">
            Not a member yet? {''}
            <button
              type="button"
              onClick={() => onRegister()}
              className="font-semibold leading-6 text-ktc-blue hover:underline"
            >
              Register a new account
            </button>
          </p>
        )}
        <div className="mt-4 bg-gray-100 p-4 space-y-4 rounded text-slate-600">
          <h3 className="font-black">Old Kitco Gold Forum Users:</h3>
          <ol className="list-decimal list-inside">
            <li>Enter your old account email.</li>
            <li>Activate your account via the confirmation email.</li>
          </ol>
          <p>We've saved your old profile and posts.</p>
          <p>
            Need help?{' '}
            <Link
              href="https://forum.kitco.com/about"
              className="text-ktc-blue font-bold hover:underline"
            >
              Click here.
            </Link>
          </p>
        </div>
      </div>
      {enableThirdProviders && (
        <>
          <hr className="my-6 h-px border-t-0 bg-transparent bg-gradient-to-r from-transparent via-neutral-500 to-transparent opacity-25 dark:via-neutral-400" />
          <div className="mt-4 sm:mx-auto sm:w-full sm:max-w-sm">
            <LoginWithThirdProviders
              onLoginError={onLoginError}
              onLoginProcessStarted={() =>
                onLoginProcessStarted('third-providers')
              }
              onNeedUsername={onNeedUsername}
              onSuccess={() => onSuccess(true)}
            />
          </div>
        </>
      )}
      <div className="mt-6 sm:mx-auto sm:w-full sm:max-w-sm">
        <LoginLegalFooter />
      </div>
    </>
  )
}

export default LoginMethods
