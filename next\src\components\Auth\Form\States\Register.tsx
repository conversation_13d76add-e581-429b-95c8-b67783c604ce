import type React from 'react'
import { BsInfoCircleFill } from 'react-icons/bs'
import Checkbox from '~/src/components/Auth/Form/Elements/Checkbox'
import Input from '~/src/components/Auth/Form/Elements/Input'
import NewPassword from '~/src/components/Auth/Form/Elements/NewPassword'
import SubmitButton from '~/src/components/Auth/Form/Elements/SubmitButton'
import LoginLegalFooter from '~/src/components/Auth/Form/LoginLegalFooter'
import AccountCreated from '~/src/components/Auth/Messages/AccountCreated'
import type { UserData } from '~/src/components/Auth/Types/UserData'
import Tooltip from '~/src/components/Tooltip/Tooltip'
import useRegister from '~/src/hooks/Auth/useRegister'

/**
 * Props for the RegisterForm component.
 */
interface RegisterProps {
  backToLogin?: () => void
  itsRecovering?: boolean
  onCustomTitle?: (title: string) => void
  onError?: (error: string) => void
  onIsRegistering?: (isRegistering: boolean) => void
  onSuccess?: () => void
  redirectToForum?: boolean
  showBackButton?: boolean
  userData?: UserData
}

/**
 * Register form component.
 *
 * @param {RegisterProps} props - Component properties
 * @returns {React.ReactElement} - Rendered component
 */
const Register: React.FC<RegisterProps> = ({
  backToLogin,
  itsRecovering = false,
  onCustomTitle,
  onError,
  onIsRegistering,
  onSuccess,
  redirectToForum = false,
  showBackButton = true,
  userData,
}: RegisterProps): React.ReactElement => {
  const {
    displayName,
    displayUsername,
    email,
    errors,
    formIsValid,
    handleChange,
    handleRegister,
    isForumDisplayName,
    isForumUsername,
    isRegistering,
    newsletter,
    setPasswordError,
    showVerificationMessage,
  } = useRegister(
    onCustomTitle,
    onError,
    onIsRegistering,
    redirectToForum,
    userData,
  )

  return (
    <>
      <div className="sm:mx-auto sm:w-full sm:max-w-sm">
        {!showVerificationMessage ? (
          <form className="mt-4 space-y-6" onSubmit={handleRegister}>
            {/* Email input */}
            <div>
              <Input
                label="Email Address"
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required={true}
                value={email}
                onChange={handleChange}
                error={errors.email}
                disabled={itsRecovering}
              />
            </div>

            {/* Display Name input */}
            <div>
              <Input
                label="Display Name"
                id="display_name"
                name="display_name"
                type="text"
                autoComplete="name"
                required={true}
                value={displayName}
                disabled={
                  isForumDisplayName &&
                  displayName !== '' &&
                  !errors.display_name
                }
                onChange={handleChange}
                error={errors.display_name}
                hint={
                  isForumDisplayName
                    ? 'You already have a display name on the forum. You can change it in the profile later.'
                    : ''
                }
              />
            </div>

            {/* UserName input */}
            <div>
              <Input
                label="Username"
                id="username"
                name="username"
                type="text"
                autoComplete="username"
                required={true}
                value={displayUsername}
                disabled={
                  isForumUsername && displayUsername !== '' && !errors.username
                }
                onChange={handleChange}
                error={errors.username}
                hint={
                  isForumUsername
                    ? 'You already have a username on the forum. You can change it in the profile later.'
                    : ''
                }
              />
            </div>

            {/* Password input */}
            <NewPassword onChange={handleChange} onError={setPasswordError} />

            <div>
              <Checkbox
                id="newsletter"
                checked={newsletter}
                onChange={handleChange}
                error={errors.newsletter}
              >
                <Tooltip
                  boxClassName="bg-neutral-50 text-xs text-black shadow-xl opacity-100"
                  disableAutoPosition={true}
                  content={
                    <div>
                      <p>
                        Kitco Forum updates will include notifications about
                        technical issues, important news, and other critical
                        information to keep you informed and ensure a smooth
                        forum experience.
                      </p>
                      <p>&nbsp;</p>
                      <p>
                        We respect your privacy and will not use your email for
                        promotional purposes.
                      </p>
                    </div>
                  }
                  spacing={15}
                  width="w-60"
                >
                  <div className="inline-flex gap-1 text-sm items-center">
                    I agree to receive email updates from Kitco Forum
                    <BsInfoCircleFill className="text-ktc-blue h-4 w-4" />
                  </div>
                </Tooltip>
              </Checkbox>
            </div>

            {/* Submit button */}
            <div>
              <SubmitButton
                isSending={isRegistering}
                disabledCondition={!formIsValid || isRegistering}
                buttonText="Register"
                loadingText="Registering..."
              />

              {showBackButton && (
                <div className="mt-4 text-center text-sm">
                  <button
                    type="button"
                    onClick={backToLogin}
                    className="font-semibold text-ktc-blue hover:text-ktc-black"
                  >
                    Back to Login
                  </button>
                </div>
              )}
            </div>
          </form>
        ) : (
          <AccountCreated onClose={onSuccess} />
        )}
      </div>

      <div className="mt-6 sm:mx-auto sm:w-full sm:max-w-sm">
        <LoginLegalFooter />
      </div>
    </>
  )
}

export default Register
