# Article Teasers
Our design provides lots of teasers, which is expected. The problem is that the teasers are technically not responsive in terms of the design in figma. The things that are responsive design, is the container around a section of teasers, so we should code according to that.
Heres the figma link: https://www.figma.com/file/awzycW00UNny0jL26bWcfc/Kitco-Replatform?type=design&node-id=3-2&mode=design&t=GuNilKQhPsqMwxCi-0

## Solution?
Encapsulate data for an article using react context. I found this approach to be significantly easier to read and maintain long term. It dramatically reduces the amount of conditionals to properly render a given teaser.

## Composition
Add components or remove them as necessary to create a teaser. 
https://github.com/favish/kitco-cms-next/blob/dev/next/src/components-news/Teasers/Teasers.tsx#L1c9

## Example
```javascript
import type { ArticleTeaserFragmentFragment } from "~/src/generated";
import { clsx } from "clsx";
import * as Teaser from "~/src/components-news/Teasers/Teasers";

export const FeaturedNext: React.FC<{
  data: ArticleTeaserFragmentFragment;
}> = ({ data }) => (
  <Teaser.CtxProvider
    node={data}
    className={clsx("flex gap-2.5 md:block", "py-2.5 md:px-5 md:first:pl-0")}
  >
    <Teaser.TImage
      width={200}
      height={100}
      className="block md:hidden w-[120px] min-w-[120px]"
      imageClassName="!rounded"
    />
    <div className="flex flex-col">
      <Teaser.Category />
      <Teaser.TitleLink className="text-[16px] leading-[130%] mb-2 pt-1" />
      <Teaser.Summary className="line-clamp-2 md:line-clamp-3" />
      <Teaser.DateTime className="pt-[0.375rem] md:pt-2" />
    </div>
  </Teaser.CtxProvider>
);
```
