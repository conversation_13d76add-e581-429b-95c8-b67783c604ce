apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-chart-images-cm
data:
  default.conf: |
    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=kitco_cache:10m max_size=1g inactive=60m use_temp_path=off;

    resolver ******* *******;
    server {
      listen 80;

      # Rewrite chart images to use cloudfront
      location /chart-images/ {
        rewrite ^/chart-images/(.*) /$1 break;
        proxy_pass https://d1hecnlhloejvu.cloudfront.net;
        proxy_set_header Host d1hecnlhloejvu.cloudfront.net;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_ssl_server_name on;
        proxy_ssl_name d1hecnlhloejvu.cloudfront.net;

        # Enable caching
        proxy_cache kitco_cache;
        # Cache valid responses for 1 minute
        proxy_cache_valid 200 1m;
        # Cache other responses for 1 minute (errors, timeouts, updating)
        proxy_cache_valid any 1m;
        # If no response or error, use stale cache
        proxy_cache_use_stale error timeout updating;
        # Try to avoid stampeding: block requests while waiting for cache
        proxy_cache_lock on;
      }

      # Healthcheck (optional)
      location /hc {
        return 200 "OK";
      }
    }
