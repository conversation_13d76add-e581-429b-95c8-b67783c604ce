import {
  applyActionCode,
  checkActionCode,
  sendPasswordResetEmail,
} from 'firebase/auth'
import type React from 'react'
import { useEffect, useState } from 'react'
import { CgSpinner } from 'react-icons/cg'
import LoginLegalFooter from '~/src/components/Auth/Form/LoginLegalFooter'
import { auth } from '~/src/services/firebase/config'

interface RecoverEmailProps {
  code: string
  onError: (message: string) => void
  onBack: () => void
}

const RecoverEmail: React.FC<RecoverEmailProps> = ({
  code,
  onError,
  onBack,
}) => {
  // State for the processing state
  const [isProcessing, setIsProcessing] = useState<boolean>(false)

  // State for the email restored
  const [emailRestored, setEmailRestored] = useState<boolean>(false)

  // State for the email
  const [email, setEmail] = useState<string>('')

  // State for the error
  const [error, setError] = useState<boolean>(false)

  /**
   * Handle the recovery of the email
   */
  const handleRecoverEmail = async () => {
    setIsProcessing(true)
    setError(false)
    setEmailRestored(false)

    try {
      // Confirm the action code is valid.
      const info = await checkActionCode(auth, code)
      const restoredEmail = info.data.email

      // Revert to the old email.
      await applyActionCode(auth, code)
      setEmail(restoredEmail)

      // Send a password reset email.
      await sendPasswordResetEmail(auth, restoredEmail)

      // Email restored
      setEmailRestored(true)
    } catch (error) {
      onError('Error recovering email: Invalid or expired action code.')
      setError(true)
      console.error('Error recovering email', error)
    } finally {
      setIsProcessing(false)
    }
  }

  /**
   * Handle the effect of the code changing
   */
  useEffect(() => {
    handleRecoverEmail()
  }, [code])

  return (
    <>
      <div className="sm:mx-auto sm:w-full sm:max-w-sm">
        {isProcessing ? (
          <>
            <div className="my-4 flex w-full items-center justify-center text-center">
              <CgSpinner
                className="h-12 w-12 animate-spin text-ktc-blue"
                aria-hidden="true"
              />
            </div>
            <p>Processing your request. Please wait...</p>
          </>
        ) : error ? (
          <>
            <h3 className="mt-4 text-lg font-bold">
              Something went wrong. Please try again.
            </h3>
            <p className="mt-4">
              Error recovering email: Invalid or expired action code.
              <br />
              Please request a new email recovery link.
            </p>
          </>
        ) : emailRestored ? (
          <>
            <h3 className="mb-4 text-lg font-bold">Email Restored</h3>
            <p className="mt-4">
              Your email has been restored to <strong>{email}</strong> and a
              password reset email has been sent. Please check your email
            </p>
          </>
        ) : null}

        <div className="mt-4 flex flex-col">
          <button
            type="button"
            onClick={onBack}
            className="mt-4 font-semibold text-ktc-blue hover:text-ktc-black"
          >
            Back to login
          </button>
        </div>
      </div>
      <div className="mt-6 sm:mx-auto sm:w-full sm:max-w-sm">
        <LoginLegalFooter />
      </div>
    </>
  )
}

export default RecoverEmail
